import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/presentation/common_widgets/growable_textfield.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/widgets/details_card_widget.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class NotificationLongTextCard2 extends StatefulWidget {
  final double SPACE_1 = 8;
  final double SPACE_2 = 16;
  final NotificationModel notificationModel;
  final bool isNewNotification;
  final bool showEditIconFromDbConfig;
  final Function() onLongTextSave;
  final Function() onLongTextCancel;
  const NotificationLongTextCard2({
    Key? key,
    required this.notificationModel,
    required this.isNewNotification,
    required this.onLongTextSave,
    required this.onLongTextCancel,
    required this.showEditIconFromDbConfig,
  }) : super(key: key);

  @override
  State<NotificationLongTextCard2> createState() =>
      _NotificationLongTextCard2State();
}

class _NotificationLongTextCard2State extends State<NotificationLongTextCard2> {
  bool isEditedMode = false;
  bool isEditable = false;
  late NotificationModel longTextModel;
  late TextEditingController longTextController;

  @override
  void initState() {
    if (widget.isNewNotification) {
      isEditedMode = true;
      isEditable = false;
    } else {
      if (widget.showEditIconFromDbConfig == true) {
        isEditable = true;
      } else {
        isEditable = false;
      }
    }
    longTextModel = NotificationModel(
      longTextView: widget.notificationModel.longTextView,
      longTextAdd: widget.notificationModel.longTextAdd,
    );
    _initDefaultValues();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CustomLongAndShortTextField(
          isShortText: false,
          controller: longTextController,
          onChanged: (value) => widget.notificationModel.longTextAdd = value,
        )
      ],
    );
  }

  void _initDefaultValues() {
    longTextController = TextEditingController(text: longTextModel.longTextAdd);
  }

  _getCardContent() {
    return Column(
      children: [
        if (widget.notificationModel.longTextView.isNotEmpty) ...[
          LabelValueWidget(
              label: AppLocalizations.of(context)!.longTextView,
              isValueEditable: false,
              isValueReadOnly: true,
              isDynamicMultiLine: true,
              valueController: TextEditingController(
                text: widget.notificationModel.longTextView,
              ),
              onValueTap: () => {}),
          SizedBox(
            height: widget.SPACE_2,
          ),
        ],
        LabelValueWidget(
          label: AppLocalizations.of(context)!.longText,
          isValueEditable: isEditedMode,
          isValueReadOnly: false,
          valueController: longTextController,
          isDynamicMultiLine: true,
          onValueChanged: (value) {
            widget.notificationModel.longTextAdd = value;
          },
          onValueTap: () => {},
        ),
        SizedBox(
          height: widget.SPACE_2,
        ),
      ],
    );
  }
}
