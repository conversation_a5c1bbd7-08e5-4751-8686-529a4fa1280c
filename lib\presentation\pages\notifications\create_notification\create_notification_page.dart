import 'dart:developer';
import 'package:eam/be/C_CODE_GROUP_HEADER.dart';
import 'package:eam/be/C_CODE_HEADER.dart';
import 'package:eam/be/C_NOTIF_TYPE_HEADER.dart';
import 'package:eam/be/C_PRIORITY_HEADER.dart';
import 'package:eam/be/C_WORK_CENTER_HEADER.dart';
import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/helpers/db_helper.dart';
import 'package:eam/helpers/equipment_helper.dart';
import 'package:eam/helpers/functional_location_helper.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/models/single_select_model.dart';
import 'package:eam/presentation/eam_packages/dropdown/controller/controller.dart';
import 'package:eam/presentation/eam_packages/dropdown/widgets/dr_column.dart';
import 'package:eam/presentation/eam_packages/tab_bar/controller.dart';
import 'package:eam/presentation/common_widgets/cancel_save_row.dart';
import 'package:eam/presentation/eam_packages/dropdown/drop_down_search.dart';
import 'package:eam/presentation/common_widgets/growable_textfield.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/presentation/pages/notifications/notification_details/notification_details.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_date_card.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_detection_method.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_failure2.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_malfunc2.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_tech_object_card2.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_user_status_card.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_work_plant_card2.dart';
import 'package:eam/provider/notification/activity/notif_activity_list_provider.dart';
import 'package:eam/provider/notification/notification_card_visibility_provider.dart';
import 'package:eam/provider/notification/notification_field_value_provider.dart';
import 'package:eam/provider/notification/notification_list_provider.dart';
import 'package:eam/provider/notification/selected_notification_provider.dart';
import 'package:eam/screens/notification/add_edit_notification_page2.dart';
import 'package:eam/screens/notification/notification_detail/activity/widget/single_notif_activity_widget2.dart';
import 'package:eam/screens/orders/order_detail/general/order_add_edit_page.dart';
import 'package:eam/screens/other/single_selection_page.dart';
import 'package:eam/screens/param/route_param.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/app_dimension.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/screen_util.dart';
import 'package:eam/utils/utils.dart';
import 'package:eam/widgets/eam_toolbar2.dart';
import 'package:eam/widgets/label_value_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

class CreateNotificationPage extends StatefulWidget {
  static const routeName = 'add-edit-notification-new';
  static const NOTIFICATION_MODE_ADD = 'NOTIFICATION_MODE_ADD';
  static const NOTIFICATION_MODE_EDIT = 'NOTIFICATION_MODE_EDIT';
  final NotificationParam notificationParam;
  const CreateNotificationPage({Key? key, required this.notificationParam})
      : super(key: key);

  @override
  _CreateNotificationPageState createState() => _CreateNotificationPageState();
}

class _CreateNotificationPageState extends State<CreateNotificationPage> {
  bool isCreateNewNotification = false;
  late NotificationListProvider notificationListProvider;
  late SelectedNotificationProvider selectedNotificationProvider;
  late NotificationGeneralNotifier notificationGeneralProvider;
  late NotifActivityListProvider notificationActivityListProvider;
  late CardVisibilityProvider cardVisibilityProvider;
  NotificationModel techModel = NotificationModel();
  late C_NOTIF_TYPE_HEADER notifHeader;
  late NotificationModel failureModel = NotificationModel();

  TextEditingController shortTextController = TextEditingController();
  TextEditingController longTextController = TextEditingController();
  TextEditingController notificationTypeController = TextEditingController();
  TextEditingController priorityController = TextEditingController();
  TextEditingController functionalLocationController = TextEditingController();
  TextEditingController equipmentController = TextEditingController();
  TextEditingController starDateController = TextEditingController();
  TextEditingController endDateController = TextEditingController();
  TextEditingController activityCodeGroupController = TextEditingController();
  TextEditingController activityCodeController = TextEditingController();
  late final SinglePageFieldValueProvider fieldProvider;
  bool _isInit = false;
  EamTabController _eamTabController = EamTabController();

  @override
  void initState() {
    ///SELECTED NOTIFICATION PROVIDER
    selectedNotificationProvider =
        Provider.of<SelectedNotificationProvider>(context, listen: false);
    notificationGeneralProvider =
        Provider.of<NotificationGeneralNotifier>(context, listen: false);
    fieldProvider =
        Provider.of<SinglePageFieldValueProvider>(context, listen: false);
    notificationActivityListProvider =
        Provider.of<NotifActivityListProvider>(context, listen: false);
    // log("message");
    // log("${selectedNotificationProvider.notificationModel.mainPlanningPlant} ----- HI");
    // log("message");
    // techModel = NotificationModel(
    //   functionalLocation:
    //       selectedNotificationProvider.notificationModel.functionalLocation,
    //   functionalLocationDesc:
    //       selectedNotificationProvider.notificationModel.functionalLocationDesc,
    //   equipment: selectedNotificationProvider.notificationModel.equipment,
    //   equipmentDesc:
    //       selectedNotificationProvider.notificationModel.equipmentDesc,
    //   workCenter: selectedNotificationProvider.notificationModel.workCenter,
    //   workCenterDesc:
    //       selectedNotificationProvider.notificationModel.workCenterDesc,
    //   mainPlanningPlant:
    //       selectedNotificationProvider.notificationModel.mainPlanningPlant,
    //   mainPlanningPlantDesc: fieldProvider.maintenancePlanningPlant,
    // );
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      selectedNotificationProvider.clearState();
      context.read<NotificationGeneralNotifier>().clearAllStates();
      await selectedNotificationProvider.getNotificationModel(context,
          isCreateNewNotification: isCreateNewNotification,
          notificationParam: widget.notificationParam);

      debugPrint("===== notif header" +
          selectedNotificationProvider.notifHeader.p_mode.toString());
      _initDefaultValues();
      // _initDefaultValues();
      // log("${selectedNotificationProvider.notificationModel.functionalLocation} ----- HI");
      // log("${selectedNotificationProvider.notificationModel.workCenter} ----- HI");
      // _initDefaultValues();
      setState(() {
        _isInit = true;
      });
    });

    ///

    ///CARD VISIBILITY PROVIDER
    cardVisibilityProvider =
        Provider.of<CardVisibilityProvider>(context, listen: false);
    if (widget.notificationParam.type ==
        CreateNotificationPage.NOTIFICATION_MODE_ADD) {
      isCreateNewNotification = true;
    }

    failureModel = NotificationModel(
      code: failureModel.code,
      codeDesc: failureModel.codeDesc,
      codeGroup: failureModel.codeGroup,
      codeGroupDesc: failureModel.codeGroupDesc,
    );

    // techModel = NotificationModel(
    //   functionalLocation: prov.notificationModel.functionalLocation,
    //   functionalLocationDesc: prov.notificationModel.functionalLocationDesc,
    //   equipment: prov.notificationModel.equipment,
    //   equipmentDesc: prov.notificationModel.equipmentDesc,
    //   workCenter: prov.notificationModel.workCenter,
    //   workCenterDesc: prov.notificationModel.workCenterDesc,
    //   mainPlanningPlant: prov.notificationModel.mainPlanningPlant,
    //   mainPlanningPlantDesc: fieldProvider.maintenancePlanningPlant,
    // );

    // _initDefaultValues();

    super.initState();
  }

  // void _initDefaultValues() {
  //   functionalLocationController = TextEditingController(
  //     text:
  //         '${techModel.functionalLocation} ${techModel.functionalLocationDesc.isNotEmpty ? '(${techModel.functionalLocationDesc})' : 'Select'}',
  //   );
  //   equipmentController = TextEditingController(
  //     text:
  //         '${techModel.equipment} ${techModel.equipmentDesc.isNotEmpty ? '(${techModel.equipmentDesc})' : 'Select'}',
  //   );
  //   workCenterController = TextEditingController(
  //     text:
  //         '${techModel.workCenter} ${techModel.workCenterDesc.isNotEmpty ? '(${techModel.workCenterDesc})' : 'Select'}',
  //   );
  //   mpController = TextEditingController(
  //     text: '${techModel.mainPlanningPlant}',
  //   );
  // }

  @override
  void dispose() {
    // shortTextController.dispose();
    // longTextController.dispose();
    // notificationTypeController.dispose();
    // priorityController.dispose();
    // functionalLocationController.dispose();
    // equipmentController.dispose();
    // starDateController.dispose();
    // endDateController.dispose();
    // activityCodeGroupController.dispose();
    // activityCodeController.dispose();
    // selectedNotificationProvider.clearState();
    // context.read<NotificationGeneralNotifier>().clearAllStates();
    // notificationGeneralProvidRRer.clearAllStates();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    notificationListProvider =
        Provider.of<NotificationListProvider>(context, listen: false);

    // log(techModel.functionalLocationDesc.toString());
    // selectedNotificationProvider =
    //     Provider.of<SelectedNotificationProvider>(context);
    return GestureDetector(
      onTap: () => Provider.of<DropDownController>(context, listen: false)
          .removeOverLay(),
      child: Scaffold(
        appBar: _getAppBar(),
        // _getAddEditNotificationAppBar(),
        body: _isInit ? _body() : CircularProgressIndicator(),
        bottomNavigationBar: _getBottomRow(),

        // _getBody(),
      ),
    );
  }

  _getAppBar() {
    return EamAppBar(context: context, title: _getTitle());
  }

  _body() {
    var isTabPortraitOrMobile =
        TabPortraitOrMobile.isTabPortraitOrMobile(context);
    if (!selectedNotificationProvider.fetchingNotificationModel) {
      _enabledDisAbleCard();
      return Padding(
        padding: Dimensions.padding(context),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(height: isTabPortraitOrMobile ? 0 : 10),
            Expanded(
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                child: DRColumn(
                  // crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (selectedNotificationProvider
                        .notificationModel.infoMessage.isNotEmpty) ...[
                      _displayErrorWidget(),
                    ],
                    _generalCard(),
                    SizedBox(height: 30),
                    // _techObjectCard(),
                    _techObjectCard2(),

                    // SizedBox(height: 30),
                    _failureCard(),

                    // _workCenterAndPlant(),
                    _workCenterAndPlant2(),
                    SizedBox(height: 30),

                    _datesCard(),
                    SizedBox(height: 30),
                    _malFunctionCard(),
                    _detectionMethod(),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    }
  }

  // _header() {
  //   return EamToolbar(
  //     title: AppLocalizations.of(context)!.createNotification,
  //     enableBack: true,
  //   );
  // return Row(
  //   mainAxisAlignment: MainAxisAlignment.start,
  //   children: [
  //     IconButton(
  //       icon: Icon(
  //         Icons.arrow_back_ios,
  //       ),
  //       onPressed: () => Navigator.pop(context),
  //     ),
  //     SizedBox(width: 5),
  //     Text("Create Notification", style: AppStyles.headLine1),
  //   ],
  // );
  // }

  _getBottomRow() {
    return CancelSaveButtonsRow(
      cancelOnTap: () {
        //NavigationService.goFirstRoute();
        Navigator.pop(context);
      },
      saveOnTap: () {
        saveValues();
      },
    );
  }

  // _getAddEditNotificationAppBar() => EamAppBar(
  //       title: _getTitle(),
  //       actionButton: _getActionButton(),
  //     );

  _getVisibility({required CardVisibilityProvider prov}) {
    //print(selectedNotificationProvider.notifTypeHeader!.cat_type_coding);
    if (selectedNotificationProvider.selectedNotifTypeHeader != null &&
        Utils.isNullOrEmpty(selectedNotificationProvider
            .selectedNotifTypeHeader!.cat_type_coding)) {
      return false;
    } else {
      //visible
      return prov.enabledFailureCard == true;
    }
  }

  // _navigateToSingleSelectionPage({required String type}) {
  //   if (type == 'notification') {
  //     NavigationService.pushNamed(
  //       SingleSelectionPage2.routeName,
  //       arguments: SingleScreenIntent(
  //           fieldPMode: widget.notifHeader.objectStatus == ObjectStatus.add
  //               ? Constants.P_MODE_ADD
  //               : Constants.P_MODE_MODIFY,
  //           tableName: C_NOTIF_TYPE_HEADER.TABLE_NAME),
  //     ).then((value) async {
  //       if (value is SingleSelectModel) {
  //         widget.notificationModel.notificationType = value.id;
  //         widget.notificationModel.notificationTypeDesc = value.description;
  //         notificationTypeController.text =
  //             '${value.id} (${value.description})';
  //         /*widget.notificationModel.priority = '';
  //         widget.notificationModel.priorityDesc = '';
  //         priorityController.text = '';*/
  //         widget.notifTypeHeader =
  //             await NotificationHelper.getNotifTypeHeader(notifType: value.id);
  //         bool enabledFailureCard = false;
  //         bool enabledmalFunctionCard = false;
  //         if (Constants.NOTIF_TYPE_TPM_ACTIVITIES == (value.id)) {
  //           enabledFailureCard = true;
  //         } else {
  //           enabledFailureCard = false;
  //         }

  //         if (Constants.NOTIF_TYPE_MALFUNCTION_REP == (value.id)) {
  //           enabledmalFunctionCard = true;
  //         } else {
  //           enabledmalFunctionCard = false;
  //         }
  //         widget.callBackListener(enabledFailureCard, enabledmalFunctionCard);
  //         setState(() {});
  //       }
  //     });
  //   } else if (type == 'priority') {
  //     if (Utils.isNullOrEmpty(widget.notificationModel.notificationType)) {
  //       UIHelper.showSnackBar(context, message: 'Select Notification Type');
  //       return;
  //     }

  //     NavigationService.pushNamed(
  //       SingleSelectionPage2.routeName,
  //       arguments: SingleScreenIntent(
  //           fieldNotificationType: widget.notificationModel.notificationType,
  //           tableName: C_PRIORITY_HEADER.TABLE_NAME),
  //     ).then((value) {
  //       if (value is SingleSelectModel) {
  //         setState(() {
  //           widget.notificationModel.priority = value.id;
  //           widget.notificationModel.priorityDesc = value.description;
  //           priorityController.text = '${value.id} (${value.description})';
  //         });
  //       }
  //     });
  //   }
  // }
  _generalCard() {
    return Consumer<SelectedNotificationProvider>(
        builder: (context, proObj, child) {
      List<Widget> children = [
        Flexible(
          flex: 1,
          fit: FlexFit.loose,
          child: EamDropDownSearch(
            labelName: AppLocalizations.of(context)!.notificationType,
            isRequiredField: true,
            orderEditIntent: SingleScreenIntent(
              fieldPMode: proObj.notifHeader.objectStatus == ObjectStatus.add
                  ? Constants.P_MODE_ADD
                  : Constants.P_MODE_MODIFY,
              tableName: C_NOTIF_TYPE_HEADER.TABLE_NAME,
            ),
            onChanged: (value) async {
              final _dropdownController =
                  Provider.of<DropDownController>(context, listen: false);
              final notificationGeneralPro =
                  Provider.of<NotificationGeneralNotifier>(context,
                      listen: false);
              if (value is SingleSelectModel) {
                proObj.notificationModel.notificationType = value.id;
                proObj.notificationModel.notificationTypeDesc =
                    value.description;
                notificationTypeController.text =
                    '${value.id} (${value.description})';
                /*widget.notificationModel.priority = '';
                         widget.notificationModel.priorityDesc = '';
                         priorityController.text = '';*/
                notifHeader = (await NotificationHelper.getNotifTypeHeader(
                    notifType: value.id))!;
                // ignore: unnecessary_null_comparison
                notificationGeneralPro
                    .setNotification(proObj.notificationModel);
                if (notifHeader != null) {
                  notificationGeneralPro.setHeader(notifHeader);
                  proObj.setNotificationTypeHeader(value.id);
                }

                bool enabledFailureCard = false;
                bool enabledmalFunctionCard = false;

                if (Constants.NOTIF_TYPE_TPM_ACTIVITIES == (value.id)) {
                  enabledFailureCard = true;
                  cardVisibilityProvider.updateUI(
                      enabledFailureCard: enabledFailureCard,
                      enableMalFunctionCard: true);
                } else {
                  enabledFailureCard = false;
                  cardVisibilityProvider.updateUI(
                      enabledFailureCard: enabledFailureCard,
                      enableMalFunctionCard: true);
                }

                if (Constants.NOTIF_TYPE_MALFUNCTION_REP == (value.id)) {
                  enabledmalFunctionCard = true;
                  cardVisibilityProvider.updateUI(
                      enabledFailureCard: enabledFailureCard,
                      enableMalFunctionCard: true);
                } else {
                  enabledmalFunctionCard = false;
                  cardVisibilityProvider.updateUI(
                      enabledFailureCard: enabledFailureCard,
                      enableMalFunctionCard: true);
                }
                _dropdownController.removeString(
                    dropDownType: DropDownType.priority);

                _dropdownController.removeString(
                    dropDownType: DropDownType.codeGroup);

                //set detection method
                _dropdownController.updateString(
                    dropDownType: DropDownType.activityCodeGroup,
                    text: Utils.formatIdDescription(
                        id: proObj.selectedNotifActivity?.act_codegrp ?? "",
                        description:
                            proObj.selectedNotifActivity?.act_codegrp_desc ??
                                ""));

                setState(() {});
              }
            },
          ),
        ),
        SizedBox(width: 16, height: 16),
        Flexible(
          fit: FlexFit.loose,
          flex: 1,
          child: EamDropDownSearch(
            labelName: AppLocalizations.of(context)!.priority,
            isRequiredField: true,
            dropDownType: DropDownType.priority,
            orderEditIntent: SingleScreenIntent(
                fieldNotificationType:
                    proObj.notificationModel.notificationType,
                tableName: C_PRIORITY_HEADER.TABLE_NAME),
            onTap: () {
              final pro =
                  Provider.of<DropDownController>(context, listen: false);
              if (Utils.isNullOrEmpty(
                  proObj.notificationModel.notificationType)) {
                pro.showDropdown(false);
                UIHelper.showSnackBar(context,
                    message: AppLocalizations.of(context)!
                        .pleaseSelectNotificationTypeString);
                return;
              } else {
                pro.showDropdown(true);
              }
            },
            onChanged: (value) {
              if (value is SingleSelectModel) {
                setState(() {
                  proObj.notificationModel.priority = value.id;
                  proObj.notificationModel.priorityDesc = value.description;
                  priorityController.text =
                      '${value.id} (${value.description})';
                });
              }
            },
          ),
        )
      ];

      return CustomCardForTexFields(
        title: AppLocalizations.of(context)!.general,
        child: Column(
          children: [
            CustomLongAndShortTextField(
              isRequiredField: true,
              isShortText: true,
              controller: shortTextController,
              onChanged: (value) => proObj.notificationModel.shortText = value,
            ),
            PlatformDetails.isMobileScreen(context)
                ? Column(mainAxisSize: MainAxisSize.min, children: children)
                : Row(children: children),
            SizedBox(height: 16),
            CustomLongAndShortTextField(
                isShortText: false,
                controller: longTextController,
                onChanged: (value) =>
                    proObj.notificationModel.longTextAdd = value),
          ],
        ),
      );
    });
  }

  _techObjectCard2() {
    return NotificationTechObjectCard2(
      notificationModel: selectedNotificationProvider.notificationModel,
      isNewNotification: isCreateNewNotification,
      showEditIconFromDbConfig:
          selectedNotificationProvider.showEditIconFromDbConfig,
      updateWorkCenter: (String workCenter) {},
      onTechObjectCancel: () {},
      onTechObjectSave: () {
        selectedNotificationProvider.saveTechObjectValues();
        selectedNotificationProvider.saveWorkCenterValues();

        selectedNotificationProvider.saveHeader(insert: false);
      },
    );
  }

  _workCenterAndPlant2() {
    return NotificationWorkPlantCard2(
      notificationModel: selectedNotificationProvider.notificationModel,
      isNewNotification: isCreateNewNotification,
      showEditIconFromDbConfig:
          selectedNotificationProvider.showEditIconFromDbConfig,
      onWorkPlantCancel: () {},
      onWorkPlantSave: () {
        selectedNotificationProvider.saveWorkCenterValues();

        selectedNotificationProvider.saveHeader(insert: false);
      },
    );
  }

  _techObjectCard() {
    return Consumer<SelectedNotificationProvider>(
        builder: (context, proObj, child) {
      List<Widget> children = [
        Flexible(
          flex: 1,
          fit: FlexFit.loose,
          child: EamDropDownSearch(
            title: functionalLocationController.text,
            labelName: AppLocalizations.of(context)!.functionalLocation,
            orderEditIntent: SingleScreenIntent(
                fieldFuncLocationId:
                    proObj.notificationModel.functionalLocation,
                fieldPMode: SingleSelectionPage.FUNCTIONAL_LIST_MODE,
                orderSearch: 'SELECTED_FUNCTION_LOCATION'),
            onChanged: (value) async {
              if (value is SingleSelectModel) {
                FUNC_LOC_HEADER? funcLocHeader =
                    await FunctionalLocationHelper.getFLocHeader(
                        floc: value.id);
                if (funcLocHeader != null) {
                  proObj.notificationModel.functionalLocation =
                      funcLocHeader.func_loc!;
                  proObj.notificationModel.functionalLocationDesc =
                      funcLocHeader.shtxt!;
                  functionalLocationController.text =
                      '${funcLocHeader.func_loc!} (${funcLocHeader.shtxt!})';

                  ///SET EQUIPMENT AS EMPTY FIELD
                  proObj.notificationModel.equipment = '';
                  proObj.notificationModel.equipmentDesc = '';
                  equipmentController.text = '';
                  //set other field value
                  if (!Utils.isNullOrEmpty(funcLocHeader.loc_main_plant)) {
                    proObj.notificationModel.mainPlanningPlant =
                        funcLocHeader.loc_main_plant!;
                    proObj.notificationModel.mainPlanningPlantDesc =
                        funcLocHeader.loc_main_plant_desc!;
                    fieldProvider.updateMaintenancePlant(
                        plant: funcLocHeader.loc_main_plant! +
                            " (" +
                            funcLocHeader.loc_main_plant_desc! +
                            ")");
                  } else {
                    proObj.notificationModel.mainPlanningPlant = '';
                    proObj.notificationModel.mainPlanningPlantDesc = '';
                    fieldProvider.updateMaintenancePlant(plant: '');
                  }
                  if (!Utils.isNullOrEmpty(funcLocHeader.org_plnt_wrk_cntr)) {
                    proObj.notificationModel.workCenterPlant =
                        funcLocHeader.org_plnt_wrk_cntr!;
                    //binding.workCenterPlant.setText(funcLocHeader.getORG_PLNT_WRK_CNTR());
                    fieldProvider.updateWorkCenterPlant(
                        workCenterPlant: funcLocHeader.org_plnt_wrk_cntr!);
                  } else {
                    proObj.notificationModel.workCenterPlant = '';
                    fieldProvider.updateWorkCenterPlant(workCenterPlant: '');
                  }
                  if (!Utils.isNullOrEmpty(funcLocHeader.org_main_wrk_cntr)) {
                    proObj.notificationModel.workCenter =
                        funcLocHeader.org_main_wrk_cntr!;
                    proObj.notificationModel.workCenterDesc =
                        funcLocHeader.org_main_wrk_cntr_desc!;
                    fieldProvider.updateWorkCenter(
                        workCenter: funcLocHeader.org_main_wrk_cntr! +
                            " (" +
                            funcLocHeader.org_main_wrk_cntr_desc! +
                            ")");
                  } else {
                    proObj.notificationModel.workCenter = '';
                    proObj.notificationModel.workCenterDesc = '';
                    fieldProvider.updateWorkCenter(workCenter: '');
                  }
                  setState(() {});
                }
              }
            },
          ),
        ),
        SizedBox(width: 16, height: 16),
        Flexible(
          flex: 1,
          fit: FlexFit.loose,
          child: EamDropDownSearch(
            title: equipmentController.text,
            labelName: AppLocalizations.of(context)!.equipment,
            orderEditIntent: SingleScreenIntent(
                fieldFuncLocationId:
                    proObj.notificationModel.functionalLocation,
                fieldPMode: SingleSelectionPage.EQUIPMENT_MODE,
                orderSearch: 'SELECTED_EQUIPMENT'),
            onChanged: (value) async {
              if (value is SingleSelectModel) {
                EQUIP_HEADER? equipHeader =
                    await EquipmentHelper.getEquipmentHeaderForDetail(
                        equipNo: value.id);
                if (equipHeader != null) {
                  proObj.notificationModel.equipment = equipHeader.equnr!;
                  proObj.notificationModel.equipmentDesc = equipHeader.shtxt!;

                  equipmentController.text =
                      '${equipHeader.equnr!} (${equipHeader.shtxt!})';

                  proObj.notificationModel.functionalLocation =
                      (equipHeader.super_func_loc!);
                  proObj.notificationModel.functionalLocationDesc =
                      (equipHeader.super_func_loc_desc!);
                  functionalLocationController.text =
                      equipHeader.super_func_loc! +
                          " (" +
                          equipHeader.super_func_loc_desc! +
                          ")";
                  if (!Utils.isNullOrEmpty(equipHeader.loc_main_plant)) {
                    proObj.notificationModel.mainPlanningPlant =
                        (equipHeader.loc_main_plant!);
                    proObj.notificationModel.mainPlanningPlantDesc =
                        (equipHeader.loc_main_plant_desc!);
                    fieldProvider.updateMaintenancePlant(
                        plant: equipHeader.loc_main_plant! +
                            " (" +
                            equipHeader.loc_main_plant_desc! +
                            ")");
                  } else {
                    proObj.notificationModel.mainPlanningPlant = '';
                    proObj.notificationModel.mainPlanningPlantDesc = '';
                    fieldProvider.updateMaintenancePlant(plant: '');
                  }
                  if (!Utils.isNullOrEmpty(equipHeader.org_plnt_wrk_cntr)) {
                    proObj.notificationModel.workCenterPlant =
                        (equipHeader.org_plnt_wrk_cntr!);
                    fieldProvider.updateWorkCenterPlant(
                        workCenterPlant: equipHeader.org_plnt_wrk_cntr!);

                    //binding.workCenterPlant.setText(equipHeader.getORG_PLNT_WRK_CNTR());
                  } else {
                    proObj.notificationModel.workCenterPlant = '';
                    fieldProvider.updateWorkCenterPlant(workCenterPlant: '');
                  }
                  if (!Utils.isNullOrEmpty(equipHeader.org_plnt_wrk_cntr)) {
                    proObj.notificationModel.workCenter =
                        (equipHeader.org_main_wrk_cntr!);
                    proObj.notificationModel.workCenterDesc =
                        (equipHeader.org_main_wrk_cntr_desc!);
                    fieldProvider.updateWorkCenter(
                        workCenter: equipHeader.org_main_wrk_cntr! +
                            " (" +
                            equipHeader.org_main_wrk_cntr_desc! +
                            ")");
                    /* binding.workCenter.setText(equipHeader.getORG_MAIN_WRK_CNTR() +
                    " (" +
                    equipHeader.getORG_MAIN_WRK_CNTR_DESC() +
                    ")");*/
                  } else {
                    proObj.notificationModel.workCenter = '';
                    proObj.notificationModel.workCenterDesc = '';
                    fieldProvider.updateWorkCenter(workCenter: '');
                  }
                  setState(() {});
                }
              }
            },
          ),
        )
      ];
      return CustomCardForTexFields(
        title: AppLocalizations.of(context)!.technicalObjects,
        child: TabPortraitOrMobile.isTabPortraitOrMobile(context)
            ? Column(mainAxisSize: MainAxisSize.min, children: children)
            : Row(children: children),
      );
    });
  }

  _failureCard() {
    return Column(
      children: [
        NotificationFailureCard2(
          notificationModel: selectedNotificationProvider.notificationModel,
          notifTypeHeader: selectedNotificationProvider.selectedNotifTypeHeader,
          isNewNotification: isCreateNewNotification,
          showEditIconFromDbConfig:
              selectedNotificationProvider.showEditIconFromDbConfig,
          onFailureCancel: () {},
          onFailureSave: () {
            selectedNotificationProvider.saveFailureModeValues();
            selectedNotificationProvider.saveHeader(insert: false);
          },
        ),
        SizedBox(height: 30)
      ],
    );
  }

  //also used as activity
  _detectionMethod() {
    return Column(
      children: [
        NotificationDetectionMethod(
          notificationModel: selectedNotificationProvider.notificationModel,
          notifTypeHeader: selectedNotificationProvider.selectedNotifTypeHeader,
          isNewNotification: isCreateNewNotification,
          showEditIconFromDbConfig:
              selectedNotificationProvider.showEditIconFromDbConfig,
          onCancel: () {},
          onSave: () {
            // selectedNotificationProvider.saveFailureModeValues();
            // selectedNotificationProvider.saveHeader(insert: false);
          },
        ),
        // SizedBox(height: 30)
      ],
    );
  }

  _malFunctionCard() {
    return Column(
      children: [
        NotificationMalFunctionCard2(
          notificationModel: selectedNotificationProvider.notificationModel,
          isNewNotification: isCreateNewNotification,
          showEditIconFromDbConfig:
              selectedNotificationProvider.showEditIconFromDbConfig,
          onMalFunctionCancel: () {},
          onMalFunctionSave: () {
            selectedNotificationProvider.saveMalfunctionValues();
            selectedNotificationProvider.saveHeader(insert: false);
          },
          context: context,
        ),
        SizedBox(height: 30),
      ],
    );
  }

  _workCenterAndPlant() {
    return Consumer<SelectedNotificationProvider>(
        builder: (context, proObj, child) {
      List<Widget> children = [
        Flexible(
          flex: 1,
          fit: FlexFit.loose,
          child: EamDropDownSearch(
            labelName: AppLocalizations.of(context)!.workCenter,
            orderEditIntent:
                SingleScreenIntent(tableName: C_WORK_CENTER_HEADER.TABLE_NAME),
            onChanged: (value) async {
              if (value is SingleSelectModel) {
                proObj.notificationModel.workCenter = value.id;
                proObj.notificationModel.workCenterDesc = value.description;
                fieldProvider.updateWorkCenter(
                    workCenter: '${value.id} (${value.description})');
                C_WORK_CENTER_HEADER? centerHeader =
                    await DbHelper.getWorkCenter(workCenter: value.id);

                if (centerHeader != null) {
                  proObj.notificationModel.workCenterPlant =
                      centerHeader.plant!;
                }
              }
            },
          ),
        ),
        SizedBox(width: 16, height: 16),
        Flexible(
          fit: FlexFit.loose,
          flex: 1,
          child: Consumer<SinglePageFieldValueProvider>(
            builder: (_, provider, __) {
              return CustomTextField(
                readOnly: true,
                labelName:
                    AppLocalizations.of(context)!.maintenancePlanningPlant,
                isRequiredField: false,
                hintText: "",
                height: 50,
                maxLines: 1,
                controller: TextEditingController(
                    text: provider.maintenancePlanningPlant.toString()),
              );
              // return LabelValueWidget(
              //     label: AppLocalizations.of(context)!.maintenancePlanningPlant,
              //     isValueEditable: false,
              //     isValueReadOnly: true,
              //     valueController: TextEditingController(
              //         text: provider.maintenancePlanningPlant),
              //     onValueTap: () => {});
            },
          ),
        ),
      ];
      return CustomCardForTexFields(
        title: AppLocalizations.of(context)!.workCenterAndPlant,
        child: TabPortraitOrMobile.isTabPortraitOrMobile(context)
            ? Column(mainAxisSize: MainAxisSize.min, children: children)
            : Row(children: children),
      );
    });
  }
  //  _getmaintenance() {
  //   return Consumer<SinglePageFieldValueProvider>(
  //     builder: (_, provider, __) {
  //       return GeneralSubTitle2(title: provider.maintenancePlanningPlant);
  //     },
  //   );
  // }

  _datesCard() {
    return CustomCardForTexFields(
      title: AppLocalizations.of(context)!.dates,
      child: Column(
        children: [
          PlatformDetails.isMobileScreen(context)
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                      Flexible(
                        fit: FlexFit.loose,
                        child: _startDate(),
                        flex: 1,
                      ),
                      SizedBox(width: 16),
                      SizedBox(height: 16),
                      Flexible(
                        fit: FlexFit.loose,
                        child: _endDate(),
                        flex: 1,
                      ),
                    ])
              : Row(
                  children: [
                    Flexible(
                      fit: FlexFit.loose,
                      child: _startDate(),
                      flex: 1,
                    ),
                    SizedBox(width: 16),
                    SizedBox(height: 16),
                    Flexible(
                      fit: FlexFit.loose,
                      child: _endDate(),
                      flex: 1,
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  void _initDefaultValues() {
    functionalLocationController =
        TextEditingController(text: widget.notificationParam.floc ?? '');
    equipmentController =
        TextEditingController(text: widget.notificationParam.equnr ?? '');

    starDateController = TextEditingController(
      text: '${Utils.isNullOrEmpty(selectedNotificationProvider.notificationModel.startDate) ? '${AppLocalizations.of(NavigationService.appNavigationKey.currentState!.context)?.date}' : Utils.getDateInDeviceFormat(timestamp: Utils.getTimestampFromServerDate(selectedNotificationProvider.notificationModel.startDate))}' +
          ' ${Utils.isNullOrEmpty(selectedNotificationProvider.notificationModel.startTime) ? '${AppLocalizations.of(NavigationService.appNavigationKey.currentState!.context)?.time}}' : Utils.getTimeInDeviceFormat(Utils.getTimestampFromServerTime(date: selectedNotificationProvider.notificationModel.startDate, time: selectedNotificationProvider.notificationModel.startTime))}',
    );
    endDateController = TextEditingController(
      text: '${Utils.isNullOrEmpty(selectedNotificationProvider.notificationModel.endDate) ? '${AppLocalizations.of(NavigationService.appNavigationKey.currentState!.context)?.date}' : Utils.getDateInDeviceFormat(timestamp: Utils.getTimestampFromServerDate(selectedNotificationProvider.notificationModel.endDate))}' +
          ' ${Utils.isNullOrEmpty(selectedNotificationProvider.notificationModel.endTime) ? '${AppLocalizations.of(NavigationService.appNavigationKey.currentState!.context)?.time}' : Utils.getTimeInDeviceFormat(Utils.getTimestampFromServerTime(date: selectedNotificationProvider.notificationModel.endDate, time: selectedNotificationProvider.notificationModel.endTime))}',
    );
  }

  _startDate() {
    return Consumer<SelectedNotificationProvider>(
      builder: (context, proObj, child) {
        return CustomDatePicker(
          labelName: AppLocalizations.of(context)!.start,
          dateController: starDateController,
          onChanged: (pickedDate) async {
            context.read<SelectedNotificationProvider>().startTime = pickedDate;

            int millisecondsSinceEpoch = Utils.convertTimeStamp(
                date: proObj.notificationModel.endDate,
                time: proObj.notificationModel.endTime);

            DateTime _endDate =
                DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);

            if (pickedDate.isAfter(_endDate)) {
              // If startDate is after endDate, set startDate to endDate
              final String selectedDate = Utils.getDateInServerFormat(
                  time: pickedDate.millisecondsSinceEpoch);
              proObj.notificationModel.startDate = selectedDate;
              proObj.notificationModel.startTime =
                  (pickedDate.hour).toString() +
                      ':' +
                      pickedDate.minute.toString() +
                      ':00';

              proObj.notificationModel.endDate = selectedDate;
              proObj.notificationModel.endTime = (pickedDate.hour).toString() +
                  ':' +
                  pickedDate.minute.toString() +
                  ':00';
              starDateController.text = Utils.getDateInDeviceFormat(
                      timestamp: pickedDate.millisecondsSinceEpoch) +
                  ' ' +
                  Utils.getTimeInDeviceFormat(Utils.getTimestampFromServerTime(
                      date: proObj.notificationModel.startDate,
                      time: (pickedDate.hour).toString() +
                          ':' +
                          pickedDate.minute.toString() +
                          ':00'));
              endDateController.text = starDateController.text;
            } else {
              final String startDate = Utils.getDateInServerFormat(
                  time: pickedDate.millisecondsSinceEpoch);

              proObj.notificationModel.startDate = startDate;
              proObj.notificationModel.startTime =
                  (pickedDate.hour).toString() +
                      ':' +
                      pickedDate.minute.toString() +
                      ':00';
              starDateController.text = Utils.getDateInDeviceFormat(
                      timestamp: pickedDate.millisecondsSinceEpoch) +
                  ' ' +
                  Utils.getTimeInDeviceFormat(Utils.getTimestampFromServerTime(
                      date: startDate,
                      time: proObj.notificationModel.startTime));
            }

            setState(() {});
          },
        );
      },
    );
  }

  _endDate() {
    return Consumer<SelectedNotificationProvider>(
        builder: (context, proObj, child) {
      return CustomDatePicker(
        labelName: AppLocalizations.of(context)!.end,
        dateController: endDateController,
        onChanged: (pickedDate) async {
          int millisecondsSinceEpoch = Utils.convertTimeStamp(
              date: proObj.notificationModel.startDate,
              time: proObj.notificationModel
                  .startTime); // Replace this with your timestamp

          DateTime _startDate =
              DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);

          if (pickedDate.isBefore(_startDate)) {
            UIHelper.showSnackBar(context,
                message: AppLocalizations.of(context)!.endTimeAfterStartTime);
            return;
          } else {
            if (pickedDate != null) {
              final String endDate = Utils.getDateInServerFormat(
                  time: pickedDate.millisecondsSinceEpoch);
              if (!Utils.isValidEndDate(
                  startDate: proObj.notificationModel.startDate,
                  endDate: endDate)) {
                UIHelper.showSnackBar(context,
                    message:
                        AppLocalizations.of(context)!.endDateAfterStartDate);
                return;
              }

              proObj.notificationModel.endDate = endDate;
              proObj.notificationModel.endTime = (pickedDate.hour).toString() +
                  ':' +
                  pickedDate.minute.toString() +
                  ':00';
              endDateController.text = Utils.getDateInDeviceFormat(
                      timestamp: pickedDate.millisecondsSinceEpoch) +
                  ' ' +
                  Utils.getTimeInDeviceFormat(Utils.getTimestampFromServerTime(
                      date: endDate, time: proObj.notificationModel.endTime));
              setState(() {});
            }
          }

          if (pickedDate == null) {
            //if user tap cancel then this function will stop
            return;
          }
        },
      );
    });
  }

  void saveValues() {
    if (!isValid()) {
      return;
    }
    selectedNotificationProvider.saveGeneralValues();
    selectedNotificationProvider.saveTechObjectValues();
    selectedNotificationProvider.saveFailureModeValues();
    selectedNotificationProvider.saveWorkCenterValues();
    selectedNotificationProvider.saveDateValues();
    selectedNotificationProvider.saveMalfunctionValues();
    selectedNotificationProvider.saveHeader(insert: true).then((key) {
      if (key.isNotEmpty) {
        //Navigator.pop(context, true);
        Provider.of<SelectedNotificationProvider>(context, listen: false)
          ..modeType = CreateNotificationPage.NOTIFICATION_MODE_ADD
          ..selectedNotifNo = key
          ..selectedNotifTypeHeader = null;

        NavigationService.popAndPushNamed(
          NotificationDetailPage2.routeName,
          arguments: NotificationParam(
            type: AddEditNotificationPage2.NOTIFICATION_MODE_ADD,
            isNewNotification: true,
          ),
        );
      } else {
        if (isCreateNewNotification) {
          UIHelper.showSnackBar(context,
              message: AppLocalizations.of(context)!.errorCreatingNotification);
        } else {
          UIHelper.showSnackBar(context,
              message: AppLocalizations.of(context)!.errorUpdatingNotification);
        }
      }
    });
  }

  String _getTitle() {
    // if (isCreateNewNotification) {
    return AppLocalizations.of(context)!.createNotification;
    // } else {
    //   if (selectedNotificationProvider.notificationModel.notificationNo
    //       .toLowerCase()
    //       .startsWith(AppLocalizations.of(context)!.newString.toLowerCase())) {
    //     return AppLocalizations.of(context)!.newString;
    //   }
    //   return selectedNotificationProvider.notificationModel.notificationNo;
    // }
  }

  void confirmAndDelete() {
    UIHelper.showEamDialog(context,
        positiveActionLabel: AppLocalizations.of(context)!.okayString,
        negativeActionLabel: AppLocalizations.of(context)!.cancel,
        title: AppLocalizations.of(context)!.alertString,
        description: AppLocalizations.of(context)!.deleteNotification,
        onPositiveClickListener: () {
      Navigator.pop(context);
      selectedNotificationProvider.deleteNotification().then((value) {
        Navigator.pop(context);
      });
    });
  }

  Future<bool> showCompleteIcon() async {
    bool allowed = !(await NotificationHelper.isNotifNocoAllowed(
        notifType: selectedNotificationProvider.notifHeader.notif_type!));
    bool present = await NotificationHelper.isNotifActionPresent(
        notifNo: selectedNotificationProvider.notifHeader.notif_no!);
    return ((!Utils.isNullOrEmpty(
                selectedNotificationProvider.notifHeader.syst_stat) &&
            selectedNotificationProvider.notifHeader.syst_stat!
                .contains(Constants.NOCO)) ||
        allowed ||
        present);
  }

  void navigateToOrderCreate() {
    Navigator.pushNamed(
      context,
      AddEditOrderPage.routeName,
      arguments: OrderParam(
        type: AddEditOrderPage.ORDER_MODE_ADD,
        notificationId: selectedNotificationProvider.notifHeader.notif_no,
        shortText: selectedNotificationProvider.notifHeader.short_text,
        equnr: selectedNotificationProvider.notifHeader.equip_id,
        funcloc: selectedNotificationProvider.notifHeader.func_loc_id,
      ),
    );
  }

  void confirmAndNoco() {
    UIHelper.showEamDialog(context,
        title: AppLocalizations.of(context)!.alertString,
        description: AppLocalizations.of(context)!.completeNotification,
        positiveActionLabel: AppLocalizations.of(context)!.yesString,
        negativeActionLabel: AppLocalizations.of(context)!.noString,
        onPositiveClickListener: () {
      Navigator.pop(context);
      selectedNotificationProvider.completeNotification().then((value) {
        Navigator.pop(context);
      });
    });
  }

  Future<bool> checkToshowAddIcon() async {
    bool followUpAllowed = await NotificationHelper.isFollowUpOrderAllowed(
        notifType: selectedNotificationProvider.notifHeader.notif_type!);
    bool isEditable = await NotificationHelper.isEditable(
        notifHeader: selectedNotificationProvider.notifHeader);
    return !followUpAllowed ||
        !Utils.isNullOrEmpty(
            selectedNotificationProvider.notifHeader.ordr_id) ||
        !isEditable;
  }

  void _enabledDisAbleCard() {
    if (!isCreateNewNotification) {
      cardVisibilityProvider.enabledFailureCard =
          selectedNotificationProvider.notifHeader.notif_type ==
              Constants.NOTIF_TYPE_TPM_ACTIVITIES;
      cardVisibilityProvider.enabledMalFunctionCard =
          selectedNotificationProvider.notifHeader.notif_type ==
              Constants.NOTIF_TYPE_MALFUNCTION_REP;
    }
  }

  List<Widget> _getActionButton() {
    return [
      //check if action button should be visible or not
      if (!selectedNotificationProvider.fetchingNotificationModel) ...[
        if (isCreateNewNotification) ...[
          IconButton(
            onPressed: () {
              saveValues();
            },
            icon: FaIcon(
              FontAwesomeIcons.check,
            ),
          ),
        ] else ...[
          FutureBuilder<bool>(
            future: checkToshowAddIcon(),
            builder: (context, snapshot) {
              if (snapshot.hasData &&
                  snapshot.data != null &&
                  snapshot.data != true &&
                  selectedNotificationProvider.notifHeader.objectStatus !=
                      ObjectStatus.add) {
                return IconButton(
                  onPressed: () {
                    navigateToOrderCreate();
                  },
                  icon: FaIcon(
                    FontAwesomeIcons.plus,
                  ),
                );
              }
              return Container();
            },
          ),
          if (selectedNotificationProvider.notifHeader.objectStatus ==
                  ObjectStatus.add &&
              selectedNotificationProvider.showEditIconFromDbConfig) ...[
            IconButton(
              onPressed: () {
                confirmAndDelete();
              },
              icon: Icon(
                Icons.delete,
              ),
            ),
          ],
          FutureBuilder<bool>(
            future: showCompleteIcon(),
            builder: (context, snapshot) {
              if (snapshot.hasData &&
                  snapshot.data != null &&
                  snapshot.data != true) {
                return IconButton(
                  onPressed: () {
                    confirmAndNoco();
                  },
                  icon: Icon(
                    Icons.flag,
                  ),
                );
              }
              return Container();
            },
          )
        ],
      ],
    ];
  }

  _displayErrorWidget() {
    return Card(
      color: Color(0XFFFCE4EC),
      elevation: ScreenUtils.ELEVATION_DETAIL_CARD,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppLocalizations.of(context)!.errorString,
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.red,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            SizedBox(
              height: ScreenUtils.SPACE_LABEL_VALUE_SECTION,
            ),
            Text(selectedNotificationProvider.notificationModel.infoMessage)
          ],
        ),
      ),
    );
  }

  bool isValid() =>
      validateNotificationGeneralCard(
          viewModel: selectedNotificationProvider.notificationModel,
          context: context) &&
      validateNotificationTechObject(
          viewModel: selectedNotificationProvider.notificationModel,
          context: context) &&
      validateNotificationWorkCenter(
          viewModel: selectedNotificationProvider.notificationModel,
          context: context);
}

bool validateNotificationGeneralCard(
    {required NotificationModel viewModel, required BuildContext context}) {
  if (Utils.isNullOrEmpty(viewModel.shortText)) {
    UIHelper.showSnackBar(context,
        message: AppLocalizations.of(context)!.enterShortText);

    return false;
  }
  if (Utils.isNullOrEmpty(viewModel.notificationType)) {
    UIHelper.showSnackBar(context,
        message: AppLocalizations.of(context)!.selectNotificationType);
    return false;
  }
  if (Utils.isNullOrEmpty(viewModel.priority)) {
    UIHelper.showSnackBar(context,
        message: AppLocalizations.of(context)!.selectPriorityString);

    return false;
  }

  return true;
}

bool validateNotificationTechObject(
    {required NotificationModel viewModel, required BuildContext context}) {
  if (Utils.isNullOrEmpty(viewModel.functionalLocation)) {
    UIHelper.showSnackBar(context,
        message: AppLocalizations.of(context)!.selectFuncLocation);
    return false;
  }
  return true;
}

bool validateNotificationWorkCenter(
    {required NotificationModel viewModel, required BuildContext context}) {
  if (Utils.isNullOrEmpty(viewModel.workCenter)) {
    UIHelper.showSnackBar(context,
        message: AppLocalizations.of(context)!.selectWorkCenter);
    return false;
  }
  return true;
}
