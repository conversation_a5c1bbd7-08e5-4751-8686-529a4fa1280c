<project name="EAM" default="writebuildno" basedir="." xmlns:ivy="antlib:org.apache.ivy.ant">

 	<taskdef resource="net/sf/antcontrib/antlib.xml">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.6.jar"/>
		</classpath>
	</taskdef>	

    <!-- Get the release number -->
    <target name="getbuildno">
        <property environment="env" />

        <!-- Now read into the build numberfile into release.str property -->
        <loadfile property="release.str"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>

        <echo message="Using release number : ${release.str}"/>
    </target>

    <target name="updatesource" depends="getbuildno">

        <!-- Release string to be written -->
        <loadfile property="release.str"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>

        <property environment="env" />

		<propertyregex property="GIT_COMMIT_ABBREV" input="${env.GIT_COMMIT}" regexp="^.{1,9}" select="\0"/>
		<propertyregex input="${release.str}" property="release.num" regexp="R-(\d+)\.0*(\d+)\.0*(\d+)" replace="\1.\2.\3"/>

        <echo message="Using package version number : ${release.num}"/>
    </target>

    <target name="writebuildno" depends="updatesource">
        <property environment="env" />

        <!-- Release string to be written -->
        <loadfile property="release.num"
            srcFile="BuildNo.txt" failonerror="true">
        </loadfile>
        <echo message="Using release number : ${release.num}"/>

        <replaceregexp file="${basedir}/pubspec.yaml" match="version:(.*)" replace="version: ${release.num}+${env.BUILD_NUMBER}" byline="true"/>
        <replace file="${basedir}/build_setup/script/eam_windows.iss" token="@VERSION@" value='${release.num}'/>
    </target>
</project>
