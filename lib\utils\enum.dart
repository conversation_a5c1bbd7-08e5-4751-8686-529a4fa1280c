enum NavbarState { home, order, round, notification }

enum RoundStatus { All, Pending, Finished }

enum MaterialsType {
  all("All"),
  //spareParts("Spare Parts"),
  spareParts("Century Spare parts"),
  maintenaceAssembly("Maintenace Assembly");

  final String value;

  const MaterialsType(this.value);
}

enum OrderObjectListViewType {
  equipement("Equipement"),
  floc("FLoc"),
  notification("Notification");

  final String value;
  const OrderObjectListViewType(this.value);
}
