import 'dart:developer';

import 'package:eam/be/C_CODE_GROUP_HEADER.dart';
import 'package:eam/be/C_NOTIF_TYPE_HEADER.dart';
import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/NOTIF_ACTION.dart';
import 'package:eam/be/NOTIF_ACTIVITY.dart';
import 'package:eam/be/NOTIF_HEADER.dart';
import 'package:eam/be/NOTIF_LONG_TEXT_ADD.dart';
import 'package:eam/helpers/equipment_helper.dart';
import 'package:eam/helpers/functional_location_helper.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/order_helper.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/provider/order/location_provider.dart';
import 'package:eam/screens/notification/add_edit_notification_page.dart';
import 'package:eam/screens/param/route_param.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

class SelectedNotificationProvider extends ChangeNotifier {
  static const className = 'SelectedNotificationProvider';
  bool fetchingNotifHeader = false;
  String modeType = AddEditNotificationPage.NOTIFICATION_MODE_ADD;
  NOTIF_HEADER notifHeader = new NOTIF_HEADER(notif_no: "New");
  C_NOTIF_TYPE_HEADER? selectedNotifTypeHeader;
  NOTIF_ACTIVITY? selectedNotifActivity;
  NotificationModel notificationModel = NotificationModel(notificationNo: '');
  NotificationModel notificationModel2 = NotificationModel(notificationNo: '');
  bool fetchingNotificationModel = false;
  bool showEditIconFromDbConfig = false;
  DateTime? startTime;

  ///selected data
  late String selectedNotifNo;
  late NOTIF_HEADER selectedNotifHeader;

  ///

  getNotificationByNotificationNo({required String notifNo}) async {
    fetchingNotifHeader = true;
    selectedNotifHeader =
        (await NotificationHelper.getNotifHeader(notifNo: notifNo))!;

    fetchingNotifHeader = false;
    notifyListeners();
  }

/*
  updateHeader({required NOTIF_HEADER orderHeader}) async {
    await AppDatabaseManager()
        .update(DBInputEntity(NOTIF_HEADER.TABLE_NAME, orderHeader.toJson()));
    notifyListeners();
  }*/

  ///IS EDITABLE
  isEditable() async {
    notifHeader =
        (await NotificationHelper.getNotifHeader(notifNo: selectedNotifNo))!;
    showEditIconFromDbConfig =
        await NotificationHelper.isEditable(notifHeader: notifHeader);
    notifyListeners();
  }

  setfailureMode(NotificationModel notificationModel) {
    notificationModel.codeGroup = notificationModel.codeGroup;
    notificationModel.code = notificationModel.code;
    notifyListeners();
  }

  //getModel
  getNotificationModel(BuildContext context,
      {required bool isCreateNewNotification,
      required NotificationParam notificationParam}) async {
    fetchingNotificationModel = true;
    if (isCreateNewNotification == true) {
      try {
        final int currentTime = DateTime.now().millisecondsSinceEpoch;
        notifHeader = NOTIF_HEADER(
          notif_no: "New",
        );
        notifHeader.req_start_dat =
            Utils.getDateInServerFormat(time: currentTime);
        notifHeader.req_start_tim =
            Utils.getTimeInServerFormat(time: currentTime);
        notifHeader.req_end_dat =
            Utils.getDateInServerFormat(time: currentTime);
        notifHeader.req_end_tim =
            Utils.getTimeInServerFormat(time: currentTime);

        // malFun
        notifHeader.malfn_start_dat =
            Utils.getDateInServerFormat(time: currentTime);
        notifHeader.malfn_start_time =
            Utils.getTimeInServerFormat(time: currentTime);
        notifHeader.malfn_end_dat =
            Utils.getDateInServerFormat(time: currentTime);
        notifHeader.malfn_end_tim =
            Utils.getTimeInServerFormat(time: currentTime);
        //
        notifHeader.notif_no = AppLocalizations.of(context)!.newString +
            notifHeader.lid.toString();

        notifHeader.objectStatus = ObjectStatus.add;
      } catch (e) {
        Logger.logError('sourceClass', 'sourceMethod', e.toString());
        UIHelper.showSnackBar(context,
            message: AppLocalizations.of(context)!.sorrySomethingWentWrong);
      }
    } else {
      notifHeader =
          (await NotificationHelper.getNotifHeader(notifNo: selectedNotifNo))!;
      selectedNotifTypeHeader = (await NotificationHelper.getNotifTypeHeader(
          notifType: notifHeader.notif_type!));
    }
    notificationModel2 = await NotificationHelper.getNotificationViewModel(
        notifHeader: notifHeader, notificationModel: NotificationModel());

    notificationModel = await NotificationHelper.getNotificationViewModel(
        notifHeader: notifHeader, notificationModel: NotificationModel());

    final List<InfoMessageData>? infoMsgs =
        (await InfoMessageHelper().getInfoMessageByBeLid(notifHeader.lid))
            .cast<InfoMessageData>();

    notificationModel.infoMessage = '';
    for (InfoMessageData data in infoMsgs!) {
      notificationModel.infoMessage =
          notificationModel.infoMessage + data.message + '\n\n';
    }
    setValuesForTechObjects(notificationParam: notificationParam);

    fetchingNotificationModel = false;
    notifyListeners();
  }

  void setNotificationTypeHeader(String type) async {
    selectedNotifTypeHeader =
        (await NotificationHelper.getNotifTypeHeader(notifType: type));
    //set notification Activity
    if (selectedNotifTypeHeader?.cat_type_coding == "Z") {
      List<C_CODE_GROUP_HEADER> code_grps =
          await NotificationHelper.getCodeGroupsByParams(
              equipNo: notificationModel.equipment,
              funLoc: notificationModel.functionalLocation,
              notifType: type,
              catType: selectedNotifTypeHeader?.cat_type_activity);

      int index = code_grps.indexWhere((e) => e.code_group == "MADM0101");
      if (index != -1) {
        selectedNotifActivity = NOTIF_ACTIVITY(
          notif_no: notifHeader.notif_no,
          item_key: 0,
          item_no: 0,
          act_sort_no: 0,
          act_key: 0,
          p_mode: Constants.P_MODE_ADD,
          act_codegrp: code_grps[index].code_group,
          act_codegrp_desc: code_grps[index].code_group_text,
        );
      }
    }

    notifyListeners();
  }

  void saveGeneralValues() {
    if (notificationModel.shortText != (notifHeader.short_text)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.short_text = (notificationModel.shortText);
    }

    if (notificationModel.notificationType != (notifHeader.notif_type)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.notif_type = (notificationModel.notificationType);
      notifHeader.notif_type_text = (notificationModel.notificationTypeDesc);
    }

    if (notificationModel.priority != (notifHeader.priority)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.priority = (notificationModel.priority);
      notifHeader.priority_desc = (notificationModel.priorityDesc);
    }
    // notifyListeners();
  }

  void saveTechObjectValues() {
    if (notificationModel.equipment != (notifHeader.equip_id)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.equip_id = (notificationModel.equipment);
      notifHeader.equip_desc = (notificationModel.equipmentDesc);
    }

    if (notificationModel.functionalLocation != (notifHeader.func_loc_id)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.func_loc_id = (notificationModel.functionalLocation);
      notifHeader.func_loc_desc = (notificationModel.functionalLocationDesc);
    }
  }

  void setActivityCodeAndDesc(String id, String desc) {
    selectedNotifActivity?.act_code = id;
    selectedNotifActivity?.act_code_desc = desc;
    notifyListeners();
  }

  void setActivityCodeGroupAndDesc(String id, String desc) {
    selectedNotifActivity?.act_codegrp = id;
    selectedNotifActivity?.act_codegrp_desc = desc;
    notifyListeners();
  }

  _saveOnlynew() {
    notifHeader.code_grp = notificationModel.codeGroup;
    notifHeader.code_grp_desc = notificationModel.codeGroupDesc;
    notifHeader.code = notificationModel.code;
    notifHeader.code_desc = notificationModel.codeDesc;
  }

  void saveFailureModeValues() {
    log(notificationModel.codeGroup);
    log(notificationModel.code);

    if (Constants.NOTIF_TYPE_TPM_ACTIVITIES !=
        (notificationModel.notificationType)) {
      return;
    }

    if (notificationModel.codeGroup != (notifHeader.code_grp)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.code_grp = (notificationModel.codeGroup);
      notifHeader.code_grp_desc = (notificationModel.codeGroupDesc);
    }

    if (notificationModel.code != (notifHeader.code)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.code = (notificationModel.code);
      notifHeader.code_desc = (notificationModel.codeDesc);
    }

    print(notifHeader.code_grp);
    print(notifHeader.code);
  }

  void saveWorkCenterValues() {
    if (notificationModel.mainPlanningPlant !=
        (notifHeader.maintain_plng_plant)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.maintain_plng_plant = (notificationModel.mainPlanningPlant);
      notifHeader.maintain_plant = (notificationModel.mainPlanningPlant);
      notifHeader.maintain_plant_desc =
          (notificationModel.mainPlanningPlantDesc);
    }

    if (notificationModel.plannerGroup != (notifHeader.planner_grp)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.planner_grp = (notificationModel.plannerGroup);
      notifHeader.planner_grp_desc = (notificationModel.plannerGroupDesc);
    }

    if (notificationModel.workCenterPlant != (notifHeader.main_work_cntr)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.main_wrk_cntr_plant = (notificationModel.workCenterPlant);
    }

    if (notificationModel.workCenter != (notifHeader.main_work_cntr)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.main_work_cntr = (notificationModel.workCenter);
      notifHeader.main_work_cntr_desc = (notificationModel.workCenterDesc);
    }
  }

  void saveDateValues() {
    if (notificationModel.startDate != (notifHeader.req_start_dat)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.req_start_dat = (notificationModel.startDate);
    }

    if (!Utils.isNullOrEmpty(notificationModel.startDate) &&
        notificationModel.startTime != (notifHeader.req_start_tim)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.req_start_tim = (notificationModel.startTime);
    }

    if (notificationModel.endDate != (notifHeader.req_end_dat)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.req_end_dat = (notificationModel.endDate);
    }

    if (!Utils.isNullOrEmpty(notificationModel.endDate) &&
        notificationModel.endTime != (notifHeader.req_end_tim)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.req_end_tim = (notificationModel.endTime);
    }
  }

  void saveMalfunctionValues() async {
    // if (Constants.NOTIF_TYPE_MALFUNCTION_REP !=
    //     (notificationModel.notificationType)) {
    //   return;
    // }

    if (!Utils.isNullOrEmpty(notificationModel.effectCode)) {
      notifHeader.effect = notificationModel.effectCode;
    }

    if (!Utils.isNullOrEmpty(notificationModel.systemCondtionCode)) {
      notifHeader.cond_before_malfn = notificationModel.systemCondtionCode;
    }

    if (!Utils.isNullOrEmpty(notificationModel.availabilityBeforeMalfunction)) {
      notifHeader.avl_before_malfn =
          int.parse(notificationModel.availabilityBeforeMalfunction);
    } else {
      notifHeader.avl_before_malfn = 100;
    }

    if (notificationModel.breakdownDuration == "null") {
      notificationModel.breakdownDuration = "0";
    }
    if (notificationModel.breakdownDuration.isNotEmpty) {
      notifHeader.breakdwn_durtn =
          double.parse(notificationModel.breakdownDuration);
    }

    notifHeader.breakdwn_durtn_unit = notificationModel.breakdownDurationUnit;
    if (notificationModel.malfunctionStartDate !=
        (notifHeader.malfn_start_dat)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.malfn_start_dat = (notificationModel.malfunctionStartDate);
    }

    if (!Utils.isNullOrEmpty(notificationModel.malfunctionStartDate) &&
        notificationModel.malfunctionStartTime !=
            (notifHeader.malfn_start_time)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.malfn_start_time = (notificationModel.malfunctionStartTime);
    }

    if (notificationModel.malfunctionEndDate != (notifHeader.malfn_end_dat)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.malfn_end_dat = (notificationModel.malfunctionEndDate);
    }

    if (!Utils.isNullOrEmpty(notificationModel.malfunctionEndDate) &&
        notificationModel.malfunctionEndTime != (notifHeader.malfn_end_tim)) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.malfn_end_tim = (notificationModel.malfunctionEndTime);
    }

    if (notificationModel.breakdown !=
        (Constants.ENABLED == (notifHeader.breakdwn_flag))) {
      if (ObjectStatus.add != notifHeader.objectStatus) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
      }

      notifHeader.breakdwn_flag =
          (notificationModel.breakdown ? Constants.ENABLED : "");
    }
    notifyListeners();
  }

  void saveNotificationActivityValues() {}

  Future<void> saveLongtextValues() async {
    if (Utils.isNullOrEmpty(notificationModel.longTextAdd)) {
      return;
    }

    try {
      NOTIF_LONG_TEXT_ADD longTextAdd = NOTIF_LONG_TEXT_ADD(
          notif_no: notifHeader.notif_no,
          obj_key: Constants.ITEM_NO_FOR_NOTIF_HEADER,
          obj_type: Constants.NOTIF_LONG_TEXT_CONSTANT_HEADER,
          p_mode: Constants.P_MODE_ADD,
          long_txt: notificationModel.longTextAdd.trim(),
          long_text_length: notificationModel.longTextAdd.trim().length);
      longTextAdd.fid = (notifHeader.lid);

      List<dynamic> data = await AppDatabaseManager().select(
        DBInputEntity(NOTIF_LONG_TEXT_ADD.TABLE_NAME, {})
          ..setWhereClause(
              '$FieldFid = "${longTextAdd.fid}" AND ${NOTIF_LONG_TEXT_ADD.FIELD_OBJ_TYPE} = "${Constants.NOTIF_LONG_TEXT_CONSTANT_HEADER}" AND ${NOTIF_LONG_TEXT_ADD.FIELD_OBJ_KEY} = ${Constants.ITEM_NO_FOR_NOTIF_HEADER}'),
      );

      if (data.length > 0) {
        longTextAdd.lid = data[0][FieldLid];
        await AppDatabaseManager().update(
          DBInputEntity(
            NOTIF_LONG_TEXT_ADD.TABLE_NAME,
            longTextAdd.toJson(),
          ),
        );
      } else {
        await AppDatabaseManager().insert(DBInputEntity(
            NOTIF_LONG_TEXT_ADD.TABLE_NAME, longTextAdd.toJson()));
      }
      NotificationHelper.markNotifAsUpdate(notifNo: notifHeader.notif_no!);
    } catch (e) {
      Logger.logError('sourceClass', 'sourceMethod', e.toString());
    }
  }

  Future<String> saveHeader({required bool insert}) async {
    try {
      if (insert) {
        //temp
        _saveOnlynew();
        notifHeader.p_mode = (Constants.P_MODE_ADD);
        notifHeader.reprtd_by = await Utils.getUserId();

        //ApplicationManager.getInstance().getDataManager().insert(notifHeader);
        await AppDatabaseManager().insert(
            DBInputEntity(NOTIF_HEADER.TABLE_NAME, notifHeader.toJson()));
        debugPrint("Header Saved:${notifHeader.toJson()}");
        await saveActivity();
        saveLongtextValues();
        return notifHeader.notif_no!;
        //navigateToAddDocuments();
      } else {
        //ApplicationManager.getInstance().getDataManager().update(notifHeader);
        if (notifHeader.p_mode == 'A') {
          notifHeader.p_mode = (Constants.P_MODE_ADD);
        } else {
          notifHeader.p_mode = (Constants.P_MODE_MODIFY);
        }
        print(notifHeader.breakdwn_durtn);
        await AppDatabaseManager().update(
            DBInputEntity(NOTIF_HEADER.TABLE_NAME, notifHeader.toJson()));

        // if (!Utils.isNullOrEmpty(notifHeader.ordr_id)) {
        //   OrdersHelper.markOrderAsUpdate(orderNo: notifHeader.ordr_id!);
        // }
        return notifHeader.notif_no!;
      }
    } catch (e) {
      Logger.logError('sourceClass', 'sourceMethod', e.toString());
      return e.toString();
    }
  }

  Future<void> saveActivity() async {
    if (selectedNotifActivity == null) {
      return;
    }
    try {
      int itemCnt = (await NotificationHelper.getMaxCntOfNotifActivity(
              notifHeader: notifHeader)) +
          1;

      selectedNotifActivity?.item_key = itemCnt;
      selectedNotifActivity?.item_no = itemCnt;
      selectedNotifActivity?.act_sort_no = itemCnt;
      selectedNotifActivity?.act_key = itemCnt;
      selectedNotifActivity!.act_sort_no = itemCnt;

      selectedNotifActivity?.objectStatus = ObjectStatus.add;
      selectedNotifActivity?.fid = notifHeader.lid;

      var result = await AppDatabaseManager().insert(DBInputEntity(
          NOTIF_ACTIVITY.TABLE_NAME, selectedNotifActivity!.toJson()));
      debugPrint('Activity: ${selectedNotifActivity?.toJson()}');
      print('Activity Saved:${selectedNotifActivity?.notif_no}');
    } catch (e) {
      debugPrint('Error saving activity: $e');
    } finally {
      // Clear the selected activity after saving
      // selectedNotifActivity = null;
      notifyListeners();
    }
  }

  Future<void> deleteNotification() async {
    await AppDatabaseManager()
        .delete(DBInputEntity(NOTIF_HEADER.TABLE_NAME, notifHeader.toJson()));
  }

  Future<String> completeNotification() async {
    try {
      final NOTIF_ACTION notifAction = NOTIF_ACTION(
        task_key: 0,
        notif_no: notifHeader.notif_no,
        action: Constants.NOCO,
        item_key: 0,
      );
      notifAction.fid = (notifHeader.lid);
      await AppDatabaseManager()
          .insert(DBInputEntity(NOTIF_ACTION.TABLE_NAME, notifAction.toJson()));

      if (notifHeader.objectStatus != ObjectStatus.add) {
        notifHeader.objectStatus = ObjectStatus.modify;
        notifHeader.syncStatus = SyncStatus.none;
        notifHeader.p_mode = (Constants.P_MODE_MODIFY);
        await AppDatabaseManager().update(
            DBInputEntity(NOTIF_HEADER.TABLE_NAME, notifHeader.toJson()));
      }
      return '';
    } catch (e) {
      Logger.logError(className, 'sourceMethod', e.toString());
      return e.toString();
    }
  }

  Future<void> setValuesForTechObjects(
      {required NotificationParam notificationParam}) async {
    final String? equipment = notificationParam.equnr;

    if (!Utils.isNullOrEmpty(equipment)) {
      EQUIP_HEADER? equipHeader =
          await EquipmentHelper.getEquipmentHeaderForDetail(
              equipNo: equipment!);

      setEquipmentValues(equipHeader: equipHeader!);

      return;
    }

    final String? funLoc = notificationParam.floc;
    if (!Utils.isNullOrEmpty(funLoc)) {
      FUNC_LOC_HEADER? funcLocHeader =
          await FunctionalLocationHelper.getFLocHeader(floc: funLoc!);

      setFunLocValues(funcLocHeader: funcLocHeader!);
    }
  }

  void setEquipmentValues({required EQUIP_HEADER equipHeader}) {
    notificationModel.equipment = equipHeader.equnr!;
    notificationModel.equipmentDesc = equipHeader.shtxt!;

    //binding.equipment.setText(equipHeader.getEQUNR() + " (" + equipHeader.getSHTXT() + ")");

    notificationModel.functionalLocation = equipHeader.super_func_loc!;
    notificationModel.functionalLocationDesc = equipHeader.super_func_loc_desc!;
    //binding.functionalLocation.setText(equipHeader.getSUPER_FUNC_LOC() + " (" + equipHeader.getSUPER_FUNC_LOC_DESC() + ")");

    if (!Utils.isNullOrEmpty(equipHeader.org_plnt_wrk_cntr)) {
      notificationModel.mainPlanningPlant = equipHeader.org_plnt_wrk_cntr!;
      notificationModel.mainPlanningPlantDesc =
          equipHeader.loc_main_plant_desc!;
      //binding.mainPlanningPlant.setText(equipHeader.getLOC_MAIN_PLANT() + " (" + equipHeader.getLOC_MAIN_PLANT_DESC() + ")");
    } else {
      notificationModel.mainPlanningPlant = '';
      notificationModel.mainPlanningPlantDesc = '';
      //binding.mainPlanningPlant.setText("");
    }

    if (!Utils.isNullOrEmpty(equipHeader.org_plnt_wrk_cntr)) {
      notificationModel.workCenterPlant = equipHeader.org_plnt_wrk_cntr!;
      //binding.workCenterPlant.setText(equipHeader.getORG_PLNT_WRK_CNTR());
    } else {
      notificationModel.workCenterPlant = '';
      //binding.workCenterPlant.setText("");
    }

    if (!Utils.isNullOrEmpty(equipHeader.org_main_wrk_cntr)) {
      notificationModel.workCenter = equipHeader.org_main_wrk_cntr!;
      notificationModel.workCenterDesc = equipHeader.org_main_wrk_cntr_desc!;
      //binding.workCenter.setText(equipHeader.getORG_MAIN_WRK_CNTR() + " (" + equipHeader.getORG_MAIN_WRK_CNTR_DESC() + ")");
    } else {
      notificationModel.workCenter = '';
      notificationModel.workCenterDesc = '';
      //binding.workCenter.setText("");
    }
  }

  void setFunLocValues({required FUNC_LOC_HEADER funcLocHeader}) {
    notificationModel.functionalLocation = funcLocHeader.func_loc!;
    notificationModel.functionalLocationDesc = funcLocHeader.shtxt!;
    //binding.functionalLocation.setText(funcLocHeader.getFUNC_LOC() + " (" + funcLocHeader.getSHTXT() + ")");

    //Clear equipment
    notificationModel.equipment = '';
    notificationModel.equipmentDesc = '';

    //binding.equipment.setText("");

    if (!Utils.isNullOrEmpty(funcLocHeader.org_plnt_wrk_cntr)) {
      notificationModel.mainPlanningPlant = funcLocHeader.org_plnt_wrk_cntr!;
      notificationModel.mainPlanningPlantDesc =
          funcLocHeader.loc_main_plant_desc!;
      //binding.mainPlanningPlant.setText(funcLocHeader.getLOC_MAIN_PLANT() + " (" + funcLocHeader.getLOC_MAIN_PLANT_DESC() + ")");
    } else {
      notificationModel.mainPlanningPlant = '';
      notificationModel.mainPlanningPlantDesc = '';
      //binding.mainPlanningPlant.setText("");
    }

    if (!Utils.isNullOrEmpty(funcLocHeader.org_plnt_wrk_cntr)) {
      notificationModel.workCenterPlant = funcLocHeader.org_plnt_wrk_cntr!;
      //binding.workCenterPlant.setText(funcLocHeader.getORG_PLNT_WRK_CNTR());
    } else {
      notificationModel.workCenterPlant = '';
      //binding.workCenterPlant.setText("");
    }

    if (!Utils.isNullOrEmpty(funcLocHeader.org_main_wrk_cntr!)) {
      notificationModel.workCenter = funcLocHeader.org_main_wrk_cntr!;
      notificationModel.workCenterDesc = funcLocHeader.org_main_wrk_cntr_desc!;
      //binding.workCenter.setText(funcLocHeader.getORG_MAIN_WRK_CNTR() + " (" + funcLocHeader.getORG_MAIN_WRK_CNTR_DESC() + ")");
    } else {
      notificationModel.workCenter = '';
      notificationModel.workCenterDesc = '';
      // binding.workCenter.setText("");
    }
  }

  //create a method to clear all the states
  void clearState() {
    fetchingNotifHeader = false;
    modeType = AddEditNotificationPage.NOTIFICATION_MODE_ADD;
    notifHeader = NOTIF_HEADER(notif_no: "New");
    selectedNotifTypeHeader = null;
    selectedNotifActivity = null;
    notificationModel = NotificationModel(notificationNo: '');
    notificationModel2 = NotificationModel(notificationNo: '');
    fetchingNotificationModel = false;
    showEditIconFromDbConfig = false;
    startTime = null;
    selectedNotifNo = '';
    // selectedNotifHeader is late, so we can't set it to null, but we can reinitialize if needed
    // selectedNotifHeader = NOTIF_HEADER(notif_no: "New");
    // notifyListeners();
  }
}
