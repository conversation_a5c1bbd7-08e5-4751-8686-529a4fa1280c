import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import 'constants.dart';

class Utils {
  static const className = 'Utils';

  static Future<String> getUserId() async {
    return (await SettingsHelper().getUserName());
  }

  static bool equalsIgnoreCase(String? string1, String? string2) {
    return string1?.toLowerCase() == string2?.toLowerCase();
  }

  static String getDateInDeviceFormat(
      {int? timestamp,
      String? stringDate,
      String format = Constants.DATE_IN_DEVICE_FORMAT}) {
    late DateTime dateTime;
    if (timestamp != null) {
      dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp).toLocal();
    } else if (stringDate != null) {
      dateTime = DateTime.parse(stringDate).toLocal();
    }

    return DateFormat(format).format(dateTime).toString();
  }

// static String getDateInDeviceFormat(
//     {int? timestamp,
//     String? stringDate,
//     String format = Constants.DATE_IN_DEVICE_FORMAT}) {
//   late DateTime dateTime;
//   if (timestamp != null) {
//     dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp).toLocal();
//   } else if (stringDate != null) {
//     dateTime = DateTime.parse(stringDate).toLocal();
//   }

//   final localDateFormat = DateFormat(format, 'en_US'); // 'en_US' for the local time zone
//   return localDateFormat.format(dateTime).toString();
// }

  static int getTimestampFromServerDate(String date,
      {String format = Constants.DATE_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime = DateTime.parse(date).toUtc();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      //Logger.e(e.getMessage());
    }

    return 0;
  }

  static int getTimestampFromServerDate2(String date,
      {String format = Constants.DATE_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime = DateTime.parse(date).toLocal();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      //Logger.e(e.getMessage());
    }

    return 0;
  }

  static bool isNullOrEmpty(String? string) {
    return string == null || string.trim().length == 0;
  }

  static String getDateInServerFormat({int? time}) {
    DateTime date;
    if (time != null) {
      date = DateTime.fromMillisecondsSinceEpoch(time, isUtc: true);
    } else {
      date = DateTime.now().toUtc();
    }
    return DateFormat(Constants.DATE_IN_SERVER_FORMAT).format(date).toString();
  }

  static String getDateInServerFormat2({int? time}) {
    DateTime date;
    if (time != null) {
      date = DateTime.fromMillisecondsSinceEpoch(time);
    } else {
      date = DateTime.now().toLocal();
    }
    return DateFormat(Constants.DATE_IN_SERVER_FORMAT).format(date).toString();
  }

  // static String getTimeInDeviceFormat(int time) {
  //   DateTime date = DateTime.fromMillisecondsSinceEpoch(time).toLocal();
  //   print( DateFormat(Constants.TIME_IN_DEVICE_FORMAT).format(date).toString());
  //   return DateFormat(Constants.TIME_IN_DEVICE_FORMAT).format(date).toString();
  // }

// static String getTimeInDeviceFormat(int time) {
//   // Convert the timestamp to a DateTime object
//   DateTime date = DateTime.fromMillisecondsSinceEpoch(time).toLocal();

//   // Format the DateTime object according to the device's settings
//   String formattedTime = DateFormat(Constants.TIME_IN_DEVICE_FORMAT).format(date);

//   // Print the formatted time
//   print(formattedTime);

//   return formattedTime;
// }

  static String getTimeInDeviceFormat(int time) {
    // Convert the timestamp to a DateTime object in the device's local timezone
    DateTime date =
        DateTime.fromMillisecondsSinceEpoch(time, isUtc: true).toLocal();

    // Format the DateTime object according to the device's settings
    String formattedTime =
        DateFormat(Constants.TIME_IN_DEVICE_FORMAT).format(date);

    // Print the formatted time
    print(formattedTime);

    return formattedTime;
  }

  static int getTimestampFromServerTime(
      {required final String date,
      required final String time,
      String format = Constants.DATE_IN_SERVER_FORMAT +
          " " +
          Constants.TIME_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime =
          DateFormat(format).parse('$date $time', true).toLocal();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError(className, 'getTimestampFromServerTime', e.toString());
    }

    return 0;
  }

  static int getTimestampFromServerTime2(
      {required final String date,
      required final String time,
      String format = Constants.DATE_IN_SERVER_FORMAT +
          " " +
          Constants.TIME_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime =
          DateFormat(format).parse('$date $time', true).toLocal();
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError(className, 'getTimestampFromServerTime', e.toString());
    }

    return 0;
  }

  static bool isValidEndDate(
      {required String startDate, required String endDate}) {
    var dateFormat = DateFormat(Constants.DATE_IN_SERVER_FORMAT);
    try {
      return !dateFormat
          .parse(endDate)
          .toUtc()
          .isBefore(dateFormat.parse(startDate).toUtc());
    } catch (e) {
      log(e.toString());
    }

    return false;
  }

  static String getFullName({required String fName, required String lName}) {
    if (Utils.isNullOrEmpty(fName)) {
      return lName;
    }

    return (fName + " " + lName).trim();
  }

  static int convertTimeStamp(
      {required final String date,
      required final String time,
      String format = Constants.DATE_IN_SERVER_FORMAT +
          " " +
          Constants.TIME_IN_SERVER_FORMAT}) {
    if (isNullOrEmpty(date)) {
      return 0;
    }

    try {
      DateTime dateTime = DateFormat(format).parse('$date $time', true);
      return dateTime.millisecondsSinceEpoch;
    } catch (e) {
      Logger.logError(className, 'getTimestampFromServerTime', e.toString());
    }

    return 0;
  }

  static DateTime timeStampToDateTime(int millisecondsSinceEpoch) {
    return DateTime.fromMillisecondsSinceEpoch(millisecondsSinceEpoch);
  }

  static String getTimeInServerFormat({int? time}) {
    var dateFormat = DateFormat(Constants.TIME_IN_SERVER_FORMAT);
    DateTime date;
    if (time != null) {
      //date = DateTime(time).toUtc();
      date = DateTime.fromMillisecondsSinceEpoch(time).toUtc();
    } else {
      date = DateTime.now().toUtc();
    }
    return dateFormat.format(date);
  }

  // static String getTimeInServerFormat({required int time}) {
  //   var dateFormat = DateFormat(Constants.TIME_IN_SERVER_FORMAT);
  //   DateTime dateTime = DateTime(time).toUtc();
  // }

  static getString({required List<String?> obj, required String separator}) {
    String finalString = "";
    if (obj != null && separator != null) {
      for (int i = 0; i < obj.length; i++) {
        if (obj[i] != null && obj[i] is String) {
          if (!isNullOrEmpty(obj[i])) {
            if (i == 0) {
              finalString = obj[i].toString();
            } else {
              if (!isNullOrEmpty(finalString))
                finalString += (separator + obj[i].toString());
              else
                finalString = obj[i].toString();
            }
          }
        }
      }
    }
    return finalString;
  }

  static getRoundedValue({double? unRestrictedStock}) {
    return NumberFormat("#").format(unRestrictedStock);
  }

  static isPortrait({required BuildContext context}) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  static Future<String> convertUtcToLocal(
      {required String date,
      required String time,
      required String timeFormat,
      required String dateFormat}) async {
    try {
      // Split the date and time strings
      final dateParts = date.split('-');

      // Split the time into its components
      final timeParts = time.split(':');

      // Ensure that there are enough parts in date and time
      if (dateParts.length != 3 || timeParts.length != 3) {
        throw FormatException("Invalid date or time format");
      }

      // Extract year, month, day, hour, minute, and second
      final year = int.parse(dateParts[0]);
      final month = int.parse(dateParts[1]);
      final day = int.parse(dateParts[2]);

      // Extract hour, ensuring it's two digits
      final hour = timeParts[0].length == 1 ? "0${timeParts[0]}" : timeParts[0];
      final minute = int.parse(timeParts[1]);
      final second = int.parse(timeParts[2]);

      // Create a DateTime object in UTC
      final utcDateTime =
          DateTime.utc(year, month, day, int.parse(hour), minute, second);

      // Convert the UTC time to the local time of the device
      final localDateTime = utcDateTime.toLocal();

      // Format the local time using your custom formats
      // final dateFormatter = DateFormat("d MMM yyyy");
      // final timeFormatter = DateFormat("h:mm aa");

      final dateFormatter = DateFormat(dateFormat);
      final timeFormatter = DateFormat(timeFormat);

      final formattedDate = dateFormatter.format(localDateTime);
      final formattedTime = timeFormatter.format(localDateTime);

      return "$formattedDate $formattedTime";
    } catch (e) {
      print("Error in convertUtcToLocal: $e");
      return ""; // Handle errors gracefully
    }
  }

  static String getStatusColor(String status) {
    switch (status) {
      case Constants.REL: // Release Order
      case Constants.NOTIF_STATUS_OPEN: // Notif Open Status
        return '#E74C3C'; // Bright Red

      case Constants.CLSD: // Closed Order
        return '#BDC3C7'; // Soft Gray

      case Constants.NOTIF_STATUS_RESOLVED: // ORAS
        return '#5DADE2';

      case Constants.CRTD:
        return '#5DADE2'; // Light Blue

      case Constants.TECO: // Technically Completed Order
      case Constants.NOTIF_STATUS_CLOSED: // NOCO
        return '#2ECC71'; // Bright Green

      case Constants.PCNF:
        return '#FFA500'; // Bright Orange

      case Constants.CNF:
        return '#1F77D2'; // Sky Blue

      case Constants.NOTIF_STATUS_PENDING_REVIEW: // NOPR
        return '#FFA500'; // Bright Yellow

      default:
        return '#95A5A6';
    }
  }

  /// Helper to parse date and time strings to a DateTime object.
  /// Returns DateTime(0) if parsing fails.
  static DateTime parseDateTime(String? date, String? time) {
    if (date == null || time == null) return DateTime(0);
    try {
      // Combine date and time, e.g., "2025-07-10 12:26:13"
      return DateTime.parse('${date.trim()} ${time.trim()}');
    } catch (_) {
      return DateTime(0);
    }
  }

  static String getTypeColor(String type) {
    switch (type) {
      case Constants.ORDER_TYPE_REACTIVE_MAINTENANCE: // YA01
      case Constants.ORDER_TYPE_MAINTENANCE_PMO1: // PM01
      case Constants.NOTIF_TYPE_MALFUNCTION_REP: // M2
        return '#E74C3C'; // Bright Red

      case Constants.ORDER_TYPE_PROACTIVE_MAINTENANCE: // YA02
      case Constants.ORDER_TYPE_MAINTENANCE_PMO2: // PM02
      case Constants.NOTIF_TYPE_PROACTIVE_WORK: // Y2
        return '#F1C40F'; // Bright Yellow

      case Constants.ORDER_TYPE_CORRECTIVE_MAINTENANCE: // YBA1
      case Constants.ORDER_TYPE_MAINTENANCE_PMO3: // PM03
      case Constants.NOTIF_TYPE_REACTIVE_WORK: // Y1
        return '#FFA500'; // Bright Orange

      case Constants.ORDER_TYPE_PREVENTIVE_MAINTENANCE: // YBA2
      case Constants.ORDER_TYPE_REFURBISHMENT: // PM04
      case Constants.NOTIF_TYPE_ACTIVITY_REP: // M3
        return '#2ECC71'; // Bright Green

      case Constants.ORDER_TYPE_UNPLANNED_MAINTENANCE: // YBA3
      case Constants.ORDER_TYPE_CALIBRATION: // PM05
        return '#9B59B6'; // Bright Purple

      case Constants.NOTIF_TYPE_MAINTENANCE_REQ: // M1
        return '#5DADE2'; // Sky Blue

      // Default Color for unknown types
      default:
        return '#95A5A6'; // Light Gray
    }
  }

  static String formatIdDescription({
    String? id,
    String? description,
  }) {
    // Trim both to avoid unwanted spaces
    final trimmedId = id?.trim();
    final trimmedDesc = description?.trim();

    // If both are null/empty, return empty string
    if ((trimmedId == null || trimmedId.isEmpty) &&
        (trimmedDesc == null || trimmedDesc.isEmpty)) {
      return '';
    }

    // If only id exists
    if (trimmedDesc == null || trimmedDesc.isEmpty) {
      return trimmedId ?? '';
    }

    // If only description exists
    if (trimmedId == null || trimmedId.isEmpty) {
      return trimmedDesc;
    }

    // If both exist: "id (description)"
    return '$trimmedId ($trimmedDesc)';
  }

  static String formatIdDescriptionWithhyphen({
    String? id,
    String? description,
  }) {
    // Trim both to avoid unwanted spaces
    final trimmedId = id?.trim();
    final trimmedDesc = description?.trim();

    // If both are null/empty, return empty string
    if ((trimmedId == null || trimmedId.isEmpty) &&
        (trimmedDesc == null || trimmedDesc.isEmpty)) {
      return "NA";
    }

    // If only id exists
    if (trimmedDesc == null || trimmedDesc.isEmpty) {
      return trimmedId ?? '';
    }

    // If only description exists
    if (trimmedId == null || trimmedId.isEmpty) {
      return trimmedDesc;
    }

    // If both exist: "id - description"
    return '$trimmedId - $trimmedDesc';
  }

}

Widget flightShuttleBuilder(
  BuildContext flightContext,
  Animation<double> animation,
  HeroFlightDirection flightDirection,
  BuildContext fromHeroContext,
  BuildContext toHeroContext,
) {
  return DefaultTextStyle(
    style: DefaultTextStyle.of(toHeroContext).style,
    child: toHeroContext.widget,
  );
}

/**print long long in console*/
void printWrapped(String text) {
  final pattern = RegExp('.{1,800}'); // 800 is the size of each chunk
  pattern.allMatches(text).forEach((match) => debugPrint(match.group(0)));
}
