import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/presentation/widgets/atoms_layer/general/texts.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class GeneralTitleRow extends StatelessWidget {
  final String title;
  final VoidCallback onTapEdit;
  final bool showEdit;
  const GeneralTitleRow(
      {super.key,
      required this.title,
      required this.onTapEdit,
      required this.showEdit});

  @override
  Widget build(BuildContext context) {
    var isTabPortraitOrMobile =
        TabPortraitOrMobile.isTabPortraitOrMobile(context);
    return Container(
      //color: Colors.blueAccent,
      height: 30,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GeneralTitleText(title: title),
          showEdit
              ? BlueBorderButton(
                  buttonName: isTabPortraitOrMobile
                      ? ""
                      : " ${AppLocalizations.of(context)!.edit}",
                  onTap: onTapEdit,
                  isIconRequired: true,
                  isBorderRequired: false,
                  icon: EamIcon(iconName: EamIcon.edit, height: 20).icon(),
                )
              // InkWell(
              //     onTap: onTapEdit,
              //     child: Container(
              //       // color: Colors.blueAccent,
              //       // width: 70,
              //       height: 21,
              //       child: Row(
              //         children: [
              //           EamIcon(iconName: EamIcon.edit).icon(),
              //           SizedBox(
              //             width: MediaQuery.of(context).size.width * 0.0045,
              //           ),
              //           AutoSizeText(
              //             //  "Edit",
              //             AppLocalizations.of(context)!.edit,
              //             maxLines: 1,
              //             overflow: TextOverflow.visible,
              //             style: AppStyles.buttonTitle16_600
              //                 .copyWith(color: Colors.black),
              //           )
              //         ],
              //       ),
              //     ),
              //   )
              : SizedBox(),
        ],
      ),
    );
  }
}

class GeneralItem extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback? onTap;

  final Color? color;
  GeneralItem({
    super.key,
    required this.title,
    required this.subtitle,
    this.onTap,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        _ontapaction();
      },
      child: SizedBox(
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          title.isEmpty ? SizedBox() : GeneralSubTitle(title: title.toString()),
          Spacer(),
          GeneralSubTitle2(
            title: subtitle.toString(),
            color: color,
          )
        ]),
      ),
    );
  }

  void _ontapaction() {
    if (onTap != null) {
      onTap!();
    }
  }
}

// class GeneralItem extends StatelessWidget {
//   final String title;
//   final String subtitle;
//   final VoidCallback? onTap;

//   const GeneralItem(
//  {required title, required String subtitle, required Null Function() onTap}

//   final Color? color;
//   GeneralItem(

//       {
//         super.key,
//       required this.title,
//       required this.subtitle,
//       this.onTap,

//       this.color,
// //  this.action = false
//       });

//       });

//   @override
//   Widget build(BuildContext context) {
//     return InkWell(
//       onTap: () {
//         _ontapaction();
//       },
//       child: SizedBox(
//         child: Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
//               GeneralSubTitle(title: title.toString()),
//               Spacer(),
//               GeneralSubTitle2(
//                 title: subtitle.toString(),
//                 color: color,
//               )
//             ]),
//             // action!
//             //     ? TextButton(
//             //         onPressed: () {
//             //           _ontapaction();
//             //         },
//             //         child: Icon(
//             //           Icons.arrow_forward_ios,
//             //           size: 20,
//             //         ))
//             //     : SizedBox()
//           ],
//         ),
//       ),
//     );
//   }

//   void _ontapaction() {
//     if (onTap != null ) {
//       onTap!();
//     }
//   }

class LongTextView extends StatelessWidget {
  final String longTextAdd;
  final String? longTextView;
  final bool? isDetailView;
  const LongTextView({
    super.key,
    required this.longTextAdd,
    this.longTextView = "",
    this.isDetailView = false,
  });

  @override
  Widget build(BuildContext context) {
    int textLength =
        longTextAdd.trim().length + (longTextView ?? "").trim().length;
    return textLength >= 100
        ? SizedBox(
            height: 65,
            child: Stack(
              children: [
                Container(
                  height: 65,
                  width: double.maxFinite,
                  decoration:
                      isDetailView! ? null : AppStyles.deFaultBoxDecoration,
                  child: Padding(
                    padding: EdgeInsets.all(isDetailView! ? 0 : 12),
                    child: SingleChildScrollView(
                      physics: NeverScrollableScrollPhysics(),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          longTextView!.isNotEmpty
                              ? Text(
                                  longTextView!,
                                  maxLines: 10,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppStyles.headLine15_600,
                                )
                              : SizedBox(),
                          longTextAdd.isNotEmpty
                              ? Text(
                                  longTextAdd,
                                  maxLines: 10,
                                  overflow: TextOverflow.ellipsis,
                                  style: AppStyles.headLine15_600,
                                )
                              : SizedBox(),
                        ],
                      ),
                    ),
                  ),
                ),
                Align(
                    alignment: Alignment.bottomRight,
                    child: Padding(
                      padding: EdgeInsets.only(
                          bottom: isDetailView! ? 0 : 10, right: 10, left: 10),
                      child: _showMore(context),
                    ))
              ],
            ))
        : Container(
            width: double.maxFinite,
            decoration: isDetailView! ? null : AppStyles.deFaultBoxDecoration,
            child: Padding(
              padding: EdgeInsets.all(isDetailView! ? 0 : 12),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    (longTextView ?? "").isNotEmpty
                        ? Text(
                            longTextView ?? "",
                            maxLines: 10,
                            overflow: TextOverflow.ellipsis,
                            style: AppStyles.headLine15_600,
                          )
                        : SizedBox(),
                    longTextAdd.isNotEmpty
                        ? Text(
                            longTextAdd,
                            maxLines: 10,
                            overflow: TextOverflow.ellipsis,
                            style: AppStyles.headLine15_600,
                          )
                        : SizedBox(),
                  ],
                ),
              ),
            ),
          );
  }

  _showMore(BuildContext context) {
    return Container(
      decoration: AppStyles.deFaultBoxDecoration
          .copyWith(border: Border.all(width: 0, color: Colors.transparent)),
      child: InkWell(
        onTap: () => _showScrollableAlert(context),
        child: Padding(
          padding: const EdgeInsets.only(left: 22),
          child: Text(
            "Read more...",
            style: AppStyles.headLine13_600
                .copyWith(color: Theme.of(context).primaryColor),
          ),
        ),
      ),
    );
  }

  void _showScrollableAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Container(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  longTextView!.isNotEmpty
                      ? Text(
                          longTextView!,
                          style: AppStyles.headLine15_600,
                        )
                      : SizedBox(),
                  longTextAdd.isNotEmpty
                      ? Text(
                          longTextAdd,
                          style: AppStyles.headLine15_600,
                        )
                      : SizedBox(),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('Close'),
            ),
          ],
        );
      },
    );
  }

  // _showBottomSheet(BuildContext context) {
  //   showModalBottomSheet<void>(
  //     context: context,
  //     shape: RoundedRectangleBorder(
  //       borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(10), topRight: Radius.circular(10)),
  //     ),
  //     builder: (BuildContext context) {
  //       return Padding(
  //         padding: const EdgeInsets.symmetric(horizontal: 13),
  //         child: SingleChildScrollView(
  //           child: Column(
  //             mainAxisAlignment: MainAxisAlignment.start,
  //             mainAxisSize: MainAxisSize.min,
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: <Widget>[
  //               3.0.spaceY,
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.end,
  //                 children: [
  //                   InkWell(
  //                     onTap: () => Navigator.pop(context),
  //                     child: Padding(
  //                       padding: const EdgeInsets.all(8.0),
  //                       child: Icon(
  //                         Icons.close,
  //                       ),
  //                     ),
  //                   )
  //                 ],
  //               ),
  //               longTextView!.isNotEmpty
  //                   ? Text(
  //                       longTextView!,
  //                       style: AppStyles.headLine15_600,
  //                     )
  //                   : SizedBox(),
  //               longTextAdd.isNotEmpty
  //                   ? Text(
  //                       longTextAdd,
  //                       style: AppStyles.headLine15_600,
  //                     )
  //                   : SizedBox(),
  //               13.0.spaceY,
  //             ],
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }
}

// final _decoration = BoxDecoration(
//   color: Colors.black.withOpacity(0.5),
//   borderRadius: BorderRadius.only(
//     bottomLeft: Radius.circular(8),
//     bottomRight: Radius.circular(8),
//   ),
//   boxShadow: [
//     BoxShadow(
//       blurStyle: BlurStyle.outer,
//       color: Colors.grey.withOpacity(0.5),
//       spreadRadius: 2,
//       blurRadius: 3,
//     ),
//   ],
// );
 

// class LongTextView extends StatelessWidget {
//   final String longTextAdd;
//   final String? longTextView;
//   const LongTextView(
//       {super.key, required this.longTextAdd, this.longTextView = ""});

//   @override
//   Widget build(BuildContext context) {
//     return SizedBox(
//       height: 100,
//       child: Stack(
//         children: [
//           Container(
//             width: double.maxFinite,
//             decoration: AppStyles.deFaultBoxDecoration,
//             child: Padding(
//               padding: const EdgeInsets.all(12),
//               child: SingleChildScrollView(
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   children: [
//                     Text(
//                       "Area 83 is a peaceful and serene destination where visitors can escape the stresses of everyday life and reconnect with nature. The property offers various ...Area 83 is a peaceful and serene destination where visitors can escape the stresses of everyday life and reconnect with nature. The property offers various ...Area 83 is a peaceful and serene destination where visitors can escape the stresses of everyday life and reconnect with nature. The property offers various ...",
//                       maxLines: 3,
//                       overflow: TextOverflow.ellipsis,
//                       style: AppStyles.headLine15_600,
//                     )
//                     // longTextView!.isNotEmpty
//                     //     ? Text(
//                     //         longTextView!,
//                     //         maxLines: 10,
//                     //         overflow: TextOverflow.ellipsis,
//                     //         style: AppStyles.headLine15_600,
//                     //       )
//                     //     : SizedBox(),
//                     // longTextAdd.isNotEmpty
//                     //     ? Text(
//                     //         longTextAdd,
//                     //         maxLines: 10,
//                     //         overflow: TextOverflow.ellipsis,
//                     //         style: AppStyles.headLine15_600,
//                     //       )
//                     //     : SizedBox(),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//           Align(
//             alignment: Alignment.bottomCenter,
//             child: _showMore(context),
//           )
//         ],
//       ),
//     );
//   }

//   _showMore(BuildContext context) {
//     return Container(
//       decoration: _decoration,
//       height: 45,
//       width: MediaQuery.of(context).size.width,
//     );
//   }
// }