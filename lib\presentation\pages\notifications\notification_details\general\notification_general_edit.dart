import 'package:eam/be/C_NOTIF_TYPE_HEADER.dart';
import 'package:eam/be/C_PRIORITY_HEADER.dart';
import 'package:eam/helpers/extensions.dart';
import 'package:eam/models/single_select_model.dart';
import 'package:eam/presentation/eam_packages/dropdown/drop_down_search.dart';
import 'package:eam/presentation/eam_packages/dropdown/widgets/dr_column.dart';
import 'package:eam/presentation/common_widgets/growable_textfield.dart';
import 'package:eam/presentation/pages/notifications/create_notification/widgets/helper.dart';
import 'package:eam/provider/notification/notification_card_visibility_provider.dart';
import 'package:eam/provider/notification/selected_notification_provider.dart';
import 'package:eam/presentation/pages/notifications/widget/notification_long_text_card2.dart';
import 'package:eam/utils/app_dimension.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class NotificationGeneralEdit extends StatefulWidget {
  const NotificationGeneralEdit({super.key});

  @override
  State<NotificationGeneralEdit> createState() =>
      _NotificationGeneralEditState();
}

class _NotificationGeneralEditState extends State<NotificationGeneralEdit> {
  late SelectedNotificationProvider selectedNotificationProvider;
  late CardVisibilityProvider cardVisibilityProvider;
  late C_NOTIF_TYPE_HEADER notifHeader;
  late TextEditingController shortTextController;
  late TextEditingController priorityController;
  late TextEditingController longTextController;

  @override
  void initState() {
    selectedNotificationProvider =
        Provider.of<SelectedNotificationProvider>(context, listen: false);
    _initDefaultValues();
    super.initState();
  }

  void _initDefaultValues() {
    shortTextController = TextEditingController(
        text: selectedNotificationProvider.notificationModel.shortText
            .toString());
    priorityController = TextEditingController(
        text: selectedNotificationProvider.notificationModel.priority.isEmpty
            ? "Select"
            : "${selectedNotificationProvider.notificationModel.priority} (${selectedNotificationProvider.notificationModel.priorityDesc})");
  }

  @override
  void dispose() {
    shortTextController.dispose();
    priorityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var selectedNotificationProvider =
        Provider.of<SelectedNotificationProvider>(context, listen: false);
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: Dimensions.padding(context),
          child: CustomCardForTexFields(
            title: AppLocalizations.of(context)!.general,
            child: DRColumn(
              children: [
                CustomLongAndShortTextField(
                  isRequiredField: true,
                  isShortText: true,
                  controller: shortTextController,
                  onChanged: (value) => selectedNotificationProvider
                      .notificationModel.shortText = value,
                ),
                // CustomTextField(
                //   labelName: AppLocalizations.of(context)!.shortText,
                //   isRequiredField: true,
                //   hintText: AppLocalizations.of(context)!.enterShortText,
                //   height: 50,
                //   maxLines: 1,
                //   controller: shortTextController,
                //   onChanged: (value) => selectedNotificationProvider
                //       .notificationModel.shortText = value,
                // ),
                // SizedBox(height: 10),
                EamDropDownSearch(
                  isRequiredField: true,
                  labelName: AppLocalizations.of(context)!.priority,
                  title: priorityController.text,
                  orderEditIntent: SingleScreenIntent(
                      fieldNotificationType: selectedNotificationProvider
                          .notificationModel.notificationType,
                      tableName: C_PRIORITY_HEADER.TABLE_NAME),
                  onChanged: (value) {
                    if (value is SingleSelectModel) {
                      setState(() {
                        selectedNotificationProvider
                            .notificationModel.priority = value.id;
                        selectedNotificationProvider
                            .notificationModel.priorityDesc = value.description;
                        priorityController.text =
                            '${value.id} (${value.description})';
                      });
                    }
                  },
                ),
                16.0.spaceY,
                NotificationLongTextCard2(
                  onLongTextCancel: () {},
                  onLongTextSave: () {},
                  isNewNotification: false,
                  showEditIconFromDbConfig:
                      selectedNotificationProvider.showEditIconFromDbConfig,
                  notificationModel:
                      selectedNotificationProvider.notificationModel,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // _generalCard() {
  //   return Consumer<SelectedNotificationProvider>(
  //       builder: (context, proObj, child) {
  //     List<Widget> children = [
  //       /*  Flexible(
  //         flex: 1,
  //         fit: FlexFit.loose,
  //         child: EamDropDownSearch(
  //           labelName: "Notification Type",
  //           orderEditIntent: SingleScreenIntent(
  //             fieldPMode: proObj.notifHeader.objectStatus == ObjectStatus.add
  //                 ? Constants.P_MODE_ADD
  //                 : Constants.P_MODE_MODIFY,
  //             tableName: C_NOTIF_TYPE_HEADER.TABLE_NAME,
  //           ),
  //           onChanged: (value) async {
  //             if (value is SingleSelectModel) {
  //               proObj.notificationModel.notificationType = value.id;
  //               proObj.notificationModel.notificationTypeDesc =
  //                   value.description;
  //               notificationTypeController.text =
  //                   '${value.id} (${value.description})';
  //               /*widget.notificationModel.priority = '';
  //                        widget.notificationModel.priorityDesc = '';
  //                        priorityController.text = '';*/
  //               notifHeader = (await NotificationHelper.getNotifTypeHeader(
  //                   notifType: value.id))!;
  //               bool enabledFailureCard = false;
  //               bool enabledmalFunctionCard = false;

  //               if (Constants.NOTIF_TYPE_TPM_ACTIVITIES == (value.id)) {
  //                 enabledFailureCard = true;
  //                 cardVisibilityProvider.updateUI(
  //                     enabledFailureCard: enabledFailureCard,
  //                     enableMalFunctionCard: enabledmalFunctionCard);
  //               } else {
  //                 enabledFailureCard = false;
  //                 cardVisibilityProvider.updateUI(
  //                     enabledFailureCard: enabledFailureCard,
  //                     enableMalFunctionCard: enabledmalFunctionCard);
  //               }

  //               if (Constants.NOTIF_TYPE_MALFUNCTION_REP == (value.id)) {
  //                 enabledmalFunctionCard = true;
  //                 cardVisibilityProvider.updateUI(
  //                     enabledFailureCard: enabledFailureCard,
  //                     enableMalFunctionCard: enabledmalFunctionCard);
  //               } else {
  //                 enabledmalFunctionCard = false;
  //                 cardVisibilityProvider.updateUI(
  //                     enabledFailureCard: enabledFailureCard,
  //                     enableMalFunctionCard: enabledmalFunctionCard);
  //               }

  //               setState(() {});
  //             }
  //           },
  //         ),
  //       ), */
  //       SizedBox(width: 16, height: 16),
  //       Flexible(
  //         fit: FlexFit.loose,
  //         flex: 1,
  //         child: EamDropDownSearch(
  //           labelName: "Priority",
  //           orderEditIntent: SingleScreenIntent(
  //               fieldNotificationType:
  //                   proObj.notificationModel.notificationType,
  //               tableName: C_PRIORITY_HEADER.TABLE_NAME),
  //           onChanged: (value) {
  //             if (value is SingleSelectModel) {
  //               setState(() {
  //                 proObj.notificationModel.priority = value.id;
  //                 proObj.notificationModel.priorityDesc = value.description;
  //                 priorityController.text =
  //                     '${value.id} (${value.description})';
  //               });
  //             }
  //           },
  //         ),
  //       )
  //     ];

  //     return CustomCardForTexFields(
  //       title: "General",
  //       child: Column(
  //         children: [
  //           CustomTextField(
  //             readOnly: false,
  //             controller: shortTextController,
  //             height: 48,
  //             hintText: "Enter short text",
  //             isRequiredField: true,
  //             labelName: "Short text",
  //             maxLines: 1,
  //             onChanged: (value) {
  //               proObj.notificationModel.shortText = value;
  //               // orderGeneralModel.shortText = value;
  //             },
  //           ),
  //           SizedBox(height: 16),
  //           TabPortraitOrMobile.isTabPortraitOrMobile(context)
  //               ? Column(mainAxisSize: MainAxisSize.min, children: children)
  //               : Row(children: children),
  //           SizedBox(height: 16),
  //           CustomTextField(
  //             readOnly: false,
  //             controller: longTextController,
  //             height: 86,
  //             hintText: "Enter long text",
  //             isRequiredField: true,
  //             labelName: "Long text",
  //             maxLines: 3,
  //             onChanged: (value) {
  //               proObj.notificationModel.longTextAdd = value;
  //             },
  //           ),
  //         ],
  //       ),
  //     );
  //   });
  // }
}
