// import 'package:dropdown_search/dropdown_search.dart';
import 'dart:math';

import 'package:eam/helpers/platform_details.dart';
import 'package:eam/helpers/tab_portrait_mobile_responsive.dart';
import 'package:eam/presentation/app_styles/app_styles.dart';
import 'package:eam/presentation/eam_packages/dropdown/controller/controller.dart';
import 'package:eam/presentation/common_widgets/inkwell.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/services/navigation_service.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

const labelTextStyle = TextStyle(fontWeight: FontWeight.w600, fontSize: 14);

class CustomCardForTexFields extends StatefulWidget {
  final String? title;
  final Widget child;
  const CustomCardForTexFields({
    super.key,
    this.title,
    required this.child,
  });

  @override
  State<CustomCardForTexFields> createState() => _CustomCardForTexFieldsState();
}

class _CustomCardForTexFieldsState extends State<CustomCardForTexFields> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        widget.title != null && widget.title!.isNotEmpty
            ? Text(
                widget.title!,
                style: AppStyles.headLine16,
              )
            : SizedBox(),
        widget.title == null || widget.title!.isEmpty
            ? SizedBox()
            : SizedBox(height: 10),
        Container(
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: Color.fromRGBO(213, 218, 221, 0.5),
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: widget.child,
          ),
        )
      ],
    );
  }
}

class CustomTextField extends StatefulWidget {
  final String labelName;
  final TextEditingController controller;
  final bool isRequiredField;
  final String hintText;
  final double height;
  final int maxLines;
  final TextInputType? keyBoardType;
  final bool? readOnly;
  final ValueChanged<String>? onChanged;
  final bool isPassword;
  final List<TextInputFormatter>? inputFormatter;

  const CustomTextField({
    super.key,
    required this.labelName,
    required this.isRequiredField,
    required this.hintText,
    required this.height,
    required this.maxLines,
    this.keyBoardType,
    required this.controller,
    this.onChanged,
    this.isPassword = false,
    this.readOnly = false,
    this.inputFormatter,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  bool _showText = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              widget.labelName,
              style: labelTextStyle,
            ),
            widget.isRequiredField
                ? Text(
                    "*",
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w700,
                    ),
                  )
                : SizedBox()
          ],
        ),
        SizedBox(height: 8),
        Container(
          height: widget.height,
          width: double.maxFinite,
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Color.fromRGBO(229, 232, 234, 1),
            ),
          ),
          // constraints: BoxConstraints(
          //   minWidth: double.infinity,
          //   maxWidth: double.infinity,
          // ),
          child: TextFormField(
            inputFormatters:
                widget.inputFormatter != null ? widget.inputFormatter : [],
            keyboardType: widget.keyBoardType,
            readOnly: widget.readOnly!,
            controller: widget.controller,
            maxLines: widget.maxLines,
            onChanged: (value) {
              setState(() {
                if (widget.onChanged != null) {
                  widget.onChanged!(value);
                }
              });
            },
            obscureText: widget.isPassword && !_showText,
            enableSuggestions: !widget.isPassword,
            autocorrect: !widget.isPassword,
            decoration: InputDecoration(
                filled: true,
                fillColor: Colors.white,
                border: InputBorder.none,
                // border: OutlineInputBorder(borderSide: BorderSide.none),
                hintStyle: TextStyle(
                  fontSize: 14,
                  color: Color.fromRGBO(144, 144, 144, 1),
                ),
                hintText: widget.hintText,
                suffixIcon: widget.inputFormatter != null
                    ? _getClearIcon()
                    : _getPasswordIcon()),
          ),
        ),
      ],
    );
  }

  _getClearIcon() {
    if (widget.controller.text == '') return null;
    return IconButton(
        onPressed: () {
          setState(() {
            widget.controller.clear();
          });
        },
        icon: EamIcon(iconName: EamIcon.close, height: 12).icon());
  }

  _getPasswordIcon() {
    if (!widget.isPassword) return null;

    return IconButton(
      onPressed: () {
        setState(() {
          _showText = !_showText;
        });
      },
      icon: EamIcon(
        iconName: _showText ? EamIcon.eye_slash : EamIcon.eye,
      ).icon(),
    );
  }
}

class CustomDatePicker extends StatefulWidget {
  final String labelName;
  final TextEditingController? dateController;
  final ValueChanged<DateTime>? onChanged;
  final bool? showTime;
  final bool allowPastDate;
  final bool allowFutureAndPast;
  const CustomDatePicker({
    super.key,
    required this.labelName,
    this.dateController,
    this.onChanged,
    this.showTime = true,
    this.allowPastDate = false,
    this.allowFutureAndPast = false,
  });

  @override
  State<CustomDatePicker> createState() => _CustomDatePickerState();
}

class _CustomDatePickerState extends State<CustomDatePicker> {
  @override
  void initState() {
    //   widget.dateController!.text = "";
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.labelName,
          style: labelTextStyle,
        ),
        SizedBox(height: 5),
        Container(
          decoration: BoxDecoration(
            shape: BoxShape.rectangle,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: Color.fromRGBO(229, 232, 234, 1),
            ),
          ),
          // padding: EdgeInsets.all(15),
          height: 50,
          // height: MediaQuery.of(context).size.width / 3,
          child: TextFormField(
            controller: widget.dateController,
            //editing controller of this TextField
            decoration: InputDecoration(
              suffixIcon: Icon(Icons.calendar_month_outlined),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(borderSide: BorderSide.none),
              hintText: "DD/MM/YYYY",
              hintStyle: TextStyle(
                fontSize: 14,
                color: Color.fromRGBO(144, 144, 144, 1),
              ),
            ),
            readOnly: true,
            //set it true, so that user will not able to edit text
            onTap: () async {
              Provider.of<DropDownController>(context, listen: false)
                  .removeOverLay();
              return _selectDateTime(context);
            },
          ),
        ),
      ],
    );
  }

  Future<void> _selectDateTime(BuildContext context) async {
    DateTime fD;
    DateTime eD;
    if (widget.allowFutureAndPast) {
      fD = DateTime(2000);
      eD = DateTime(2100);
    } else if (widget.allowPastDate) {
      fD = DateTime(2000);
      eD = DateTime.now();
    } else {
      fD = DateTime.now();
      eD = DateTime(2100);
    }
    final DateTime? date = await showDatePicker(
      useRootNavigator: true,
      context: context,
      initialDate: DateTime.now(),
      firstDate: fD,
      lastDate: eD,
    );
    if (widget.showTime!) {
      if (date != null) {
        final TimeOfDay? time = await showTimePicker(
          context: context,
          initialTime: TimeOfDay.now(),
        );
        if (time != null) {
          final DateTime dateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          ).toUtc();
          // Do something with the selected DateTime object.

          //  widget.dateController!.text = DateFormat('dd-MM-yyyy').format(dateTime);

          widget.onChanged!(dateTime);
          // NavigationService.goBack();
          // Navigator.pop(context);
        }
      }
    } else {
      widget.onChanged!(date!);
    }
  }
}

class NotificationAddItemsRow extends StatefulWidget {
  final String title;
  final Widget child;
  const NotificationAddItemsRow(
      {super.key, required this.title, required this.child});

  @override
  State<NotificationAddItemsRow> createState() =>
      _NotificationAddItemsRowState();
}

class _NotificationAddItemsRowState extends State<NotificationAddItemsRow> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.title,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700),
            ),
            widget.child,
          ],
        ),
        SizedBox(height: 32),
        Divider(
          color: Color.fromRGBO(213, 218, 221, 1),
        )
      ],
    );
  }
}

class BlueBorderButton extends StatelessWidget {
  final String buttonName;
  final bool? isBorderRequired;
  final VoidCallback onTap;
  final bool? isNameRequiredOnMobile;
  final Color? buttonNameColor;
  final Color borderColor;
  final bool isIconRequired;
  final Widget? icon;
  final Color? color;
  final double? width;
  final bool? isResponsive;
  final bool? isOnTap;
  const BlueBorderButton({
    super.key,
    required this.buttonName,
    required this.onTap,
    this.isNameRequiredOnMobile = false,
    this.color,
    this.icon,
    required this.isIconRequired,
    this.buttonNameColor,
    this.width,
    this.borderColor = const Color.fromRGBO(40, 95, 231, 1),
    this.isBorderRequired = false,
    this.isResponsive = true,
    this.isOnTap = true,
  });

  @override
  Widget build(BuildContext context) {
    var isTabPortraitOrMobile = PlatformDetails.isMobileScreen(context);
    String title(String? name) {
      if (isTabPortraitOrMobile) {
        if (isNameRequiredOnMobile!) {
          return name!;
        } else {
          return '';
        }
      } else {
        return name!;
      }
    }

    return InkWellCard(
      borderRadius: 8,
      color: color ?? Colors.transparent,
      onTap: isOnTap! ? onTap : () {},
      child: Container(
        height: 46,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isBorderRequired! ? borderColor : Colors.transparent,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTabPortraitOrMobile ? 10 : 16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              isIconRequired ? icon! : SizedBox(),
              isIconRequired
                  ? SizedBox(
                      width: 12,
                    )
                  : SizedBox(),
              Text(
                title(buttonName),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: buttonNameColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class BorderButtonRightIcon extends StatelessWidget {
  final String buttonName;
  final bool? isBorderRequired;
  final VoidCallback onTap;
  final bool? isNameRequiredOnMobile;
  final Color? buttonNameColor;
  final Color borderColor;
  final bool isIconRequired;
  final Widget? icon;
  final Color? color;
  final double? width;
  final bool? isResponsive;
  final bool? isOnTap;
  const BorderButtonRightIcon({
    super.key,
    required this.buttonName,
    required this.onTap,
    this.isNameRequiredOnMobile = false,
    this.color,
    this.icon,
    required this.isIconRequired,
    this.buttonNameColor,
    this.width,
    this.borderColor = const Color.fromRGBO(40, 95, 231, 1),
    this.isBorderRequired = false,
    this.isResponsive = true,
    this.isOnTap = true,
  });

  @override
  Widget build(BuildContext context) {
    var isTabPortraitOrMobile = PlatformDetails.isMobileScreen(context);
    String title(String? name) {
      if (isTabPortraitOrMobile) {
        if (isNameRequiredOnMobile!) {
          return name!;
        } else {
          return '';
        }
      } else {
        return name!;
      }
    }

    return InkWellCard(
      borderRadius: 8,
      color: color ?? Colors.transparent,
      onTap: isOnTap! ? onTap : () {},
      child: Container(
        height: 46,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isBorderRequired! ? borderColor : Colors.transparent,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: isTabPortraitOrMobile ? 10 : 16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title(buttonName),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: buttonNameColor,
                ),
              ),
              isIconRequired
                  ? SizedBox(
                      width: 12,
                    )
                  : SizedBox(),
              isIconRequired ? icon! : SizedBox(),
            ],
          ),
        ),
      ),
    );
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return newValue;
    }

    final double? parsedValue = double.tryParse(newValue.text);
    if (parsedValue == null) {
      // Not a valid number, revert to the old value
      return oldValue;
    }

    // Allow only one decimal point and at most one digit after it
    if (newValue.text.contains('.') &&
        newValue.text.split('.').last.length > 1) {
      return oldValue;
    }

    return newValue;
  }
}
