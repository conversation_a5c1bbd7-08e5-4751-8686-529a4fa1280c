import 'dart:ui';

import 'package:eam/models/common/option_item.dart';
import 'package:eam/models/menu_action_dropdown.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/services/navigation_service.dart';
import 'package:eam/utils/app_color.dart';
import 'package:eam/widgets/tooltip_shape.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class UIHelper {
  static showEamDialog(
    BuildContext context, {
    required String title,
    String? description,
    String? positiveActionLabel,
    String? negativeActionLabel,
    String? genieAcionLabel,
    Function()? onPositiveClickListener,
    Function? onNegativeClickListener,
    Function? onGenieActionListener,
    final bool dismissible = true,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return WillPopScope(
          onWillPop: () async {
            return dismissible;
          },
          child: CupertinoAlertDialog(
            title: Text(title!),
            content: description != null ? Text(description) : null,
            actions: [
              if (genieAcionLabel != null)
                TextButton.icon(
                    onPressed: () {
                      if (onGenieActionListener != null) {
                        onGenieActionListener();
                      }
                    },
                    icon: FaIcon(
                      FontAwesomeIcons.wandMagicSparkles,
                      color: Colors.purple,
                      size: 16,
                    ),
                    label: Text(
                      genieAcionLabel,
                      style: TextStyle(
                        color: Colors.purple,
                      ),
                    )),
              if (negativeActionLabel != null)
                TextButton(
                  onPressed: () {
                    Navigator.of(context, rootNavigator: true).pop();

                    if (onNegativeClickListener != null) {
                      onNegativeClickListener();
                    }
                  },
                  child: Text(
                    negativeActionLabel,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              if (positiveActionLabel != null)
                TextButton(
                  onPressed: () {
                    if (onPositiveClickListener != null) {
                      onPositiveClickListener();
                    }
                    //Navigator.pop(context);
                  },
                  child: Text(
                    positiveActionLabel,
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  static Future<bool> showExitConfirmationDialog(
      {required BuildContext context}) async {
    bool exit = false;
    await showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('Are you sure you want to exit?'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
              },
              child: Text('No'),
            ),
            TextButton(
              onPressed: () {
                exit = true;
                Navigator.of(context).pop(true);
              },
              child: Text('Yes'),
            ),
          ],
        );
      },
    );
    return exit;
  }

  static showEamDialog2(
    BuildContext context, {
    String? title,
    String? description,
    String? positiveActionLabel,
    String? negativeActionLabel,
    VoidCallback? onPositiveClickListener,
    VoidCallback? onNegativeClickListener,
    bool dismissible = true,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(title!),
          content: description != null ? Text(description) : null,
          actions: [
            if (negativeActionLabel != null)
              TextButton(
                onPressed: () {
                  onNegativeClickListener!();
                },
                child: Text(
                  negativeActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            if (positiveActionLabel != null)
              TextButton(
                onPressed: () {
                  onPositiveClickListener!();
                },
                child: Text(
                  positiveActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  static showEamProgressDialog2(BuildContext context,
      {String? title,
      Color? progressColor,
      bool barrierDismissible = false,
      bool showCancelIcon = false}) {
    if (!kIsWeb) {
      showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        // false = user must tap button, true = tap outside dialog
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            content: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title != null) ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(child: Text(title)),
                ] else ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  )
                ]
              ],
            ),
            actions: [
              if (showCancelIcon) ...[
                TextButton(
                  onPressed: () {
                    // NavigationService.goBack();
                    UIHelper.closeDialog(context);
                  },
                  child: Text(AppLocalizations.of(context)!.cancel),
                )
              ]
            ],
          );
        },
      );
    } else {
      showGeneralDialog(
        context: context,
        barrierLabel: 'Label',
        barrierDismissible: false,
        pageBuilder: (_, __, ___) => Center(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 50, sigmaY: 50),
            child: Material(
              color: Colors.transparent,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title != null) ...[
                    EamIcon(
                            iconName: EamIcon.hourglass,
                            height: 50,
                            width: 50,
                            color: Colors.white)
                        .icon(),
                    SizedBox(
                      height: 10,
                    ),
                    Text(title,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        )),
                  ] else ...[
                    CircularProgressIndicator(
                      color: Theme.of(context).primaryColor,
                    )
                  ]
                ],
              ),
            ),
          ),
        ),
      );
    }
  }

  static showEamProgressDialog(BuildContext context,
      {String? title,
      Color? progressColor,
      bool barrierDismissible = false,
      bool showCancelIcon = false}) {
    if (!kIsWeb) {
      showDialog(
        context: context,
        barrierDismissible: barrierDismissible,
        // false = user must tap button, true = tap outside dialog
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            content: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (title != null) ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(child: Text(title)),
                ] else ...[
                  CircularProgressIndicator(
                    color: Theme.of(context).primaryColor,
                  )
                ]
              ],
            ),
            actions: [
              if (showCancelIcon) ...[
                TextButton(
                  onPressed: () {
                    NavigationService.goBack();
                    //Navigator.of(context, rootNavigator: true).pop();
                  },
                  child: Text(AppLocalizations.of(context)!.cancel),
                )
              ]
            ],
          );
        },
      );
    } else {
      showGeneralDialog(
        context: context,
        barrierLabel: 'Label',
        barrierDismissible: false,
        pageBuilder: (_, __, ___) => Center(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 50, sigmaY: 50),
            child: Material(
              color: Colors.transparent,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (title != null) ...[
                    EamIcon(
                            iconName: EamIcon.hourglass,
                            height: 50,
                            width: 50,
                            color: Colors.white)
                        .icon(),
                    SizedBox(
                      height: 10,
                    ),
                    Text(title,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        )),
                  ] else ...[
                    CircularProgressIndicator(
                      color: Theme.of(context).primaryColor,
                    )
                  ]
                ],
              ),
            ),
          ),
        ),
      );
    }
  }

  static addMenuActionButtonWithOption(BuildContext context,
      {IconData? icon,
      required List<MenuActionItem> options,
      required Function(MenuActionItem) onOptionItemSelected}) {
    return PopupMenuButton<MenuActionItem>(
      offset: const Offset(0, 50),
      shape: const TooltipShape(),
      icon: Icon(
        icon ?? Icons.more_vert,
        color: Theme.of(context).primaryColor,
      ),
      onSelected: (MenuActionItem value) {
        onOptionItemSelected(value);
      },
      itemBuilder: (BuildContext context) {
        return options.map((MenuActionItem menuOptionItem) {
          return PopupMenuItem<MenuActionItem>(
            value: menuOptionItem,
            child: Row(
              children: [
                Text(menuOptionItem.name),
              ],
            ),
          );
        }).toList();
      },
    );
  }

  static void showSnackBar(BuildContext context, {required String message}) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      action: SnackBarAction(
        label: AppLocalizations.of(context)!.dismiss,
        textColor: AppColor.whiteColor,
        onPressed: () => ScaffoldMessenger.of(context).hideCurrentSnackBar(),
      ),
    ));
  }

  static void showEamDialogWithOption(
    BuildContext context, {
    String? title,
    String? description,
    bool dismissible = false,
    required List<String> optionList,
    required Function(int index) onOptionSelected,
  }) {
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          scrollable: true,
          title: Text(title!),
          content: Column(
            children: [
              if (description != null) ...[
                Text(description),
                SizedBox(
                  height: 10,
                ),
              ],
              Container(
                width: double.maxFinite,
                height: 100,
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: optionList.length,
                  separatorBuilder: (context, index) {
                    return Divider();
                  },
                  itemBuilder: (context, index) {
                    return InkResponse(
                        onTap: () {
                          Navigator.of(context, rootNavigator: true).pop();
                          onOptionSelected(index);
                        },
                        child: Padding(
                          padding: EdgeInsets.all(5),
                          child: Text(optionList[index]),
                        ));
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static showToast(BuildContext context,
      {required String message, ToastGravity? gravity}) {
    if (gravity == null) {
      gravity = ToastGravity.BOTTOM;
    }
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: gravity,
        timeInSecForIosWeb: 1,
        backgroundColor: Theme.of(context).primaryColor,
        textColor: Colors.white,
        fontSize: 16.0);
  }

  static showEamDialogWithInput(
    BuildContext context, {
    String? title,
    String? description,
    String? positiveActionLabel,
    String? negativeActionLabel,
    Function(String res)? onPositiveClickListener,
    Function? onNegativeClickListener,
    bool dismissible = true,
    required TextEditingController controller,
    TextInputType inputType = TextInputType.text,
    String? hintText,
  }) {
    //controller.text = '';
    showDialog(
      context: context,
      barrierDismissible: dismissible,
      // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(title!),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (description != null) ...[
                Text(description),
              ],
              TextFormField(
                keyboardType: inputType,
                controller: controller,
                decoration: InputDecoration(hintText: hintText),
              ),
            ],
          ),
          actions: [
            if (negativeActionLabel != null)
              TextButton(
                onPressed: () =>
                    Navigator.of(context, rootNavigator: true).pop(),
                child: Text(
                  negativeActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            if (positiveActionLabel != null)
              TextButton(
                onPressed: () {
                  if (onPositiveClickListener != null) {
                    onPositiveClickListener(controller.text);
                  }
                  //Navigator.pop(context);
                },
                child: Text(
                  positiveActionLabel,
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  static void showBottomSheetDialogWithOption(BuildContext context,
      {String? title,
      String? description,
      required List<OptionItem> optionList,
      required Function(OptionItem selected) onOptionSelected,
      bool dismissible = false}) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(10),
        ),
      ),
      clipBehavior: Clip.antiAliasWithSaveLayer,
      builder: (BuildContext context) {
        return Stack(
          alignment: Alignment.topRight,
          children: [
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 10,
                  ),
                  if (title != null) ...[
                    Text(
                      title,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                  if (description != null) ...[
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                  Column(
                    children: optionList
                        .map((e) => ListTile(
                              onTap: () => onOptionSelected(e),
                              title: Text(e.optionName),
                              leading: e.iconData != null
                                  ? Icon(
                                      e.iconData,
                                      color: Theme.of(context).primaryColor,
                                    )
                                  : null,
                            ))
                        .toList(),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                IconButton(
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: FaIcon(
                    FontAwesomeIcons.xmark,
                    color: Colors.black,
                  ),
                )
              ],
            ),
          ],
        );
      },
    );
  }

  static getEAMAppBarShape() {
    return RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(
        bottom: Radius.circular(10),
      ),
    );
  }

  static closeDialog(BuildContext context) =>
      Navigator.of(context, rootNavigator: true).pop();

  static progressBarWidget(BuildContext context) {
    return SizedBox(
      height: 200,
      width: 200,
      child: Center(
        child: CupertinoActivityIndicator(
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  static void simpleDialog(BuildContext context,
      {String title = "", String dec = ""}) {
    UIHelper.showEamDialog(context,
        positiveActionLabel: AppLocalizations.of(context)!.okayString,
        negativeActionLabel: AppLocalizations.of(context)!.cancel,
        title: title,
        description: dec, onPositiveClickListener: () {
      Navigator.of(context, rootNavigator: true).pop();
    });
  }

  static bool hasFailureCategory(Map<String, dynamic> response) {
    if (response.containsKey('InfoMessage')) {
      final messages = response['InfoMessage'];
      if (messages is List) {
        for (var msg in messages) {
          if (msg is Map<String, dynamic> &&
              msg['category']?.toUpperCase() == 'FAILURE') {
            return true;
          }
        }
      }
    }
    return false;
  }

  static String getInfoMessage(Map<String, dynamic> response) {
    if (response.containsKey('InfoMessage')) {
      final infoList = response['InfoMessage'];
      if (infoList is List && infoList.isNotEmpty) {
        final msg = infoList.first;
        if (msg is Map<String, dynamic> && msg.containsKey('message')) {
          return msg['message'] ?? '';
        }
      }
    }
    return '';
  }
}
