import 'package:eam/be/CUSTOMIZATION_INPUT_HEADER.dart';
import 'package:eam/be/DOCUMENT_HEADER.dart';
import 'package:eam/be/EQUIP_INPUT_HEADER.dart';
import 'package:eam/be/FORM_OBJECT_HEADER.dart';
import 'package:eam/be/FUNC_LOC_INPUT_HEADER.dart';
import 'package:eam/be/MATERIAL_INPUT_HEADER.dart';
import 'package:eam/be/NOTIF_DOCUMENT.dart';
import 'package:eam/be/NOTIF_HEADER.dart';
import 'package:eam/be/NOTIF_INPUT_HEADER.dart';
import 'package:eam/be/NOTIF_SEARCH_HEADER.dart';
import 'package:eam/be/ORDER_DOCUMENT.dart';
import 'package:eam/be/ORDER_HEADER.dart';
import 'package:eam/be/ORDER_INPUT_HEADER.dart';
import 'package:eam/be/ORDER_SEARCH_HEADER.dart';
import 'package:eam/be/RIG_INPUT_HEADER.dart';
import 'package:eam/be/ROUND_HEADER.dart';
import 'package:eam/be/ROUND_INPUT_HEADER.dart';
import 'package:eam/helpers/db_helper.dart';
import 'package:eam/helpers/document_helper.dart';
import 'package:eam/helpers/forms_helper.dart';
import 'package:eam/helpers/notification_helper.dart';
import 'package:eam/helpers/order_helper.dart';
import 'package:eam/helpers/rounds_helper.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/presentation/pages/work_orders/work_orderdetails/notifier/order_notifier.dart';
import 'package:eam/screens/document/formdocument_helper.dart';
import 'package:eam/utils/app_constants.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../be/FORM_INPUT_HEADER.dart';

class PAHelper {
  static const String className = "PAHelper";

  static Future<Result> getCustomizationInAsyncMode() async {
    String userId = await Utils.getUserId();
    CUSTOMIZATION_INPUT_HEADER customization =
        CUSTOMIZATION_INPUT_HEADER(user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_CUSTOMIZATION,
            dataObject: customization.toJson());
    return result;
  }

  static Future<Result> getAllOrdersInSync() async {
    String userId = await Utils.getUserId();
    //TODO : language has to be taken from preference
    ORDER_INPUT_HEADER order = ORDER_INPUT_HEADER(user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_ORDERS,
            dataObject: order.toJson());
    return result;
  }

  static Future<Result> getAllOrdersInAsync() async {
    String userId = await Utils.getUserId();
    ORDER_INPUT_HEADER order = ORDER_INPUT_HEADER(user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_ORDERS,
            dataObject: {});
    return result;
  }

  static Future<Result> getAllRounds() async {
    String userId = await Utils.getUserId();
    //TODO : language has to be taken from preference
    // ORDER_INPUT_HEADER order = ORDER_INPUT_HEADER(user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
      umpApplicationFunctionName: AppConstants.PA_GET_ROUNDS,
    );
    return result;
  }

  static Future<Result> getAllRoundsInAsync() async {
    // String userId = await Utils.getUserId();
    // ORDER_INPUT_HEADER order = ORDER_INPUT_HEADER(user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_ROUNDS,
            dataObject: {});
    return result;
  }

  static Future<bool> isSyncAllowed() async {
    try {
      return (await SettingsHelper().getInboxCount()) <= 0;
    } catch (e) {
      throw (e);
    }
    return true;
  }

  //IMPLEMENTATION OF getOrders
  static getQueryOrderInSyncMode(
      {required ORDER_SEARCH_HEADER searchHeader}) async {
    try {
      searchHeader.user_id = await Utils.getUserId();
      searchHeader.language = await DbHelper.getLanguage();
      // Map<String, dynamic> data = {
      //   AppConstants.BE_ORDER_SEARCH: [
      //     {ORDER_SEARCH_HEADER.TABLE_NAME: searchHeader.toJson()}
      //   ]
      // };
      Map<String, dynamic> data = {};
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.query))
          .send(
              umpApplicationFunctionName: AppConstants.PA_SEARCH_ORDERS,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(className, 'getQueryOrderInSyncMode', e.toString());
      throw e;
    }
  }

  //IMPLMENTATION OF searchNotifications
  static getQueryNotificationInSyncMode(
      {required NOTIF_SEARCH_HEADER searchHeader}) async {
    try {
      searchHeader.user_id = await Utils.getUserId();
      searchHeader.language = await DbHelper.getLanguage();
      // Map<String, dynamic> data = {
      //   AppConstants.BE_NOTIF_SEARCH: [
      //     {NOTIF_SEARCH_HEADER.TABLE_NAME: searchHeader.toJson()}
      //   ]
      // };
      Map<String, dynamic> data = {};
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.query))
          .send(
              umpApplicationFunctionName: AppConstants.PA_SEARCH_NOTIFS,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(
          className, 'getQueryNotificationInSyncMode', e.toString());
      throw e;
    }
  }

  static Future<void> submitOrderInASync2(
      {required ORDER_HEADER header}) async {
    await FormDocumentHelper.getformWebDocuments(header.order_no!);
    await FormDocumentHelper.getOrderFormAttachments(
        orderNumber: header.order_no!);
    await DocumentHelper.getOrderFormsDocs(orderNumber: header.order_no!);
    await FormsHelper.fetchOrderFormObjectData(header.order_no!);
    await DocumentHelper.getPendingFormDocuments(header.order_no!);
  }

  // testEnitynames()async{
  //   List<StructureMetaData> childStructureMetas =
  //       (await (await DatabaseManager().getFrameworkDB()).allStructureMetas)
  //           .where((element) =>
  //               element.beName == beName && element.isHeader != "1")
  //           .toList();
  // }

  static Future<void> submitOrderInASync({required ORDER_HEADER header}) async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DocumentHelper.getPendingDocuments(
              tableName: ORDER_DOCUMENT.TABLE_NAME, key: header.order_no!);

      documentHeaders = documentHeaders
          .where((documentHeader) =>
              documentHeader.objectStatus != ObjectStatus.global)
          .toList();

      List<DOCUMENT_HEADER> formdocumentHeaders =
          await DocumentHelper.getPendingFormDocuments(header.order_no!);

      formdocumentHeaders = formdocumentHeaders
          .where((formdocumentHeader) =>
              formdocumentHeader.objectStatus != ObjectStatus.global)
          .toList();

      List<FORM_OBJECT_HEADER> formObjects =
          await FormsHelper.fetchOrderFormObjectData(header.order_no!);

      formObjects = formObjects
          .where((formObject) => formObject.objectStatus != ObjectStatus.global)
          .toList();

      // Submit All Documents related orders
      if (documentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in documentHeaders) {
          await submitDocumentsInAsync(documentHeader: documentHeader);
        }
      }

      // Submit All Documents related forms
      if (formdocumentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in formdocumentHeaders) {
          await submitDocumentsInAsync(documentHeader: documentHeader);
        }
      }

      // Submit All forms related Orders
      if (formObjects.isNotEmpty) {
        for (FORM_OBJECT_HEADER formObjectHeader in formObjects) {
          await submitFormsInAsync(form_object_header: formObjectHeader);
        }
      }

      if (!Utils.isNullOrEmpty(header.notif_id)) {
        NOTIF_HEADER? notifHeader =
            await NotificationHelper.getNotifHeader(notifNo: header.notif_id);

        if (notifHeader != null) {
          if (notifHeader.objectStatus != ObjectStatus.global) {
            await InfoMessageHelper().deleteInfoMessageByBeLid(notifHeader.lid);

            await submitNotificationInASync(header: notifHeader);

            //FrameworkManager.getInstance().getDataManager().delete(InfoMessage.TABLE_NAME, InfoMessage.BE_LID + " = '" + notifHeader.getLid() + "'");
          }
        }
      }
      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      if (header.objectStatus == ObjectStatus.add) {
        //SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, null, Constants.PA_CREATE_ORDER, header.getBEName(), header.getLid(), false);
        Result result = await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(true)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: AppConstants.PA_CREATE_ORDER,
                dataObject: header.toJson());
        print('result.statusCode');
        print(result.statusCode);
        if (result.statusCode == Status.submittedToOutbox) {
          print('SUBMITTED');
        } else {}
      } else {
        // var res =   await  FormsHelper.getOrderform(order:header.order_no.toString() );
        // print(res);
        // SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, null, Constants.PA_UPDATE_ORDER, header.getBEName(), header.getLid(), false);
        await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(true)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: AppConstants.PA_UPDATE_ORDER,
                dataObject: header.toJson());
      }

      //InfoMessage table not found
      //FrameworkManager.getInstance().getDataManager().delete(InfoMessage.TABLE_NAME, InfoMessage.BE_LID + " = '" + header.getLid() + "'");
    } catch (e) {
      Logger.logError("PAHelper", "submitOrderInASync", e.toString());
    }
  }

  static Future<void> submitOrderInASync_old(
      {required ORDER_HEADER header}) async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DocumentHelper.getPendingDocuments(
              tableName: ORDER_DOCUMENT.TABLE_NAME, key: header.order_no!);

      if (documentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in documentHeaders) {
          await submitDocumentsInAsync(documentHeader: documentHeader);
        }
      }

      if (!Utils.isNullOrEmpty(header.notif_id)) {
        NOTIF_HEADER? notifHeader =
            await NotificationHelper.getNotifHeader(notifNo: header.notif_id);

        if (notifHeader != null) {
          if (notifHeader.objectStatus != ObjectStatus.global) {
            await InfoMessageHelper().deleteInfoMessageByBeLid(notifHeader.lid);

            await submitNotificationInASync(header: notifHeader);

            //FrameworkManager.getInstance().getDataManager().delete(InfoMessage.TABLE_NAME, InfoMessage.BE_LID + " = '" + notifHeader.getLid() + "'");
          }
        }
      }
      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      if (header.objectStatus == ObjectStatus.add) {
        //SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, null, Constants.PA_CREATE_ORDER, header.getBEName(), header.getLid(), false);
        Result result = await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(true)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: AppConstants.PA_CREATE_ORDER,
                dataObject: header.toJson());
        print('result.statusCode');
        print(result.statusCode);
        if (result.statusCode == Status.submittedToOutbox) {
          print('SUBMITTED');
        } else {}
      } else {
        // SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, null, Constants.PA_UPDATE_ORDER, header.getBEName(), header.getLid(), false);
        await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(true)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: AppConstants.PA_UPDATE_ORDER,
                dataObject: header.toJson());
      }

      //InfoMessage table not found
      //FrameworkManager.getInstance().getDataManager().delete(InfoMessage.TABLE_NAME, InfoMessage.BE_LID + " = '" + header.getLid() + "'");
    } catch (e) {
      Logger.logError("PAHelper", "submitOrderInASync", e.toString());
    }
  }

  static Future<void> submitAllOrdersInAsync() async {
    List<ORDER_HEADER> allOrders = await OrdersHelper.getAllModifiedOrders(
        meausrementContext: Constants.MEAS_CTX_ORDER);

    for (ORDER_HEADER order in allOrders) {
      final _skipWorkOrder = await OrdersHelper.checkOperationRunning(
          orderNo: order.order_no ?? "");
      //If Work order currently running user should not allow to Submit
      if (_skipWorkOrder) {
        debugPrint("Operation running");
      } else {
        await submitOrderInASync(header: order);
      }
    }
  }

  static Future<void> submitAllOrdersInSync(
      {required BuildContext context, required Function onSubmitted}) async {
    List<ORDER_HEADER> allOrders = await OrdersHelper.getAllModifiedOrders(
        meausrementContext: Constants.MEAS_CTX_ORDER);

    for (ORDER_HEADER order in allOrders) {
      final _isOperationRunning = await OrdersHelper.checkOperationRunning(
          orderNo: order.order_no ?? "");

      if (_isOperationRunning) {
        debugPrint("Operation running");
      } else {
        await submitOrderInSync(
            header: order, context: context, onSubmitted: () {});
      }
    }
    onSubmitted();
  }

  static Future<void> submitDocumentsInAsync(
      {required DOCUMENT_HEADER documentHeader}) async {
    try {
      // await (SyncEngine()
      //       ..isAutoSave(true)
      //       ..isAsynchronous(true)
      //       ..setInputType(InputType.standard)
      //       ..setRequestType(RequestType.rqst))
      //     .send(
      //         umpApplicationFunctionName: AppConstants.PA_MODIFY_DOCUMENT,
      //         dataObject: documentHeader.toJson());
    } catch (e) {
      Logger.logError(className, 'submitDocumentsInAsync', e.toString());
    }
  }

  static Future<void> submitFormsInAsync(
      {required FORM_OBJECT_HEADER form_object_header}) async {
    try {
      // var res = await (SyncEngine()
      //       ..isAutoSave(true)
      //       ..isAsynchronous(true)
      //       ..setInputType(InputType.standard)
      //       ..setRequestType(RequestType.rqst))
      //     .send(
      //         umpApplicationFunctionName: AppConstants.EAM_PA_SUBMIT_FORM,
      //         dataObject: form_object_header.toJson());
      // print(res);
    } catch (e) {
      Logger.logError(className, 'submitOrderFormsInAsync', e.toString());
    }
  }

  static Future<void> submitFormsInSync(
      {required FORM_OBJECT_HEADER form_object_header}) async {
    try {
      // var res = await (SyncEngine()
      //       ..isAutoSave(true)
      //       ..isAsynchronous(false)
      //       ..setInputType(InputType.standard)
      //       ..setRequestType(RequestType.rqst))
      //     .send(
      //         umpApplicationFunctionName: AppConstants.EAM_PA_SUBMIT_FORM,
      //         dataObject: form_object_header.toJson());
      // print(res);
    } catch (e) {
      Logger.logError(className, 'submitOrderFormsInAsync', e.toString());
    }
  }

  static Future<void> submitNotificationInASync(
      {required NOTIF_HEADER header, Function? onSubmitted}) async {
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DocumentHelper.getPendingDocuments(
              tableName: NOTIF_DOCUMENT.TABLE_NAME, key: header.notif_no!);

      documentHeaders = documentHeaders
          .where((documentHeader) =>
              documentHeader.objectStatus != ObjectStatus.global)
          .toList();

      List<FORM_OBJECT_HEADER> formObjects =
          await FormsHelper.fetchNotificationFormObjectData(header.notif_no!);

      formObjects = formObjects
          .where((formObject) => formObject.objectStatus != ObjectStatus.global)
          .toList();

      if (documentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in documentHeaders) {
          await submitDocumentsInAsync(documentHeader: documentHeader);
        }
      }

      // Submit All forms related Notifications
      if (formObjects.isNotEmpty) {
        for (FORM_OBJECT_HEADER formObjectHeader in formObjects) {
          await submitFormsInAsync(form_object_header: formObjectHeader);
        }
      }
      await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
              umpApplicationFunctionName: Constants.P_MODE_ADD == header.p_mode
                  ? AppConstants.PA_CREATE_NOTIF
                  : AppConstants.PA_UPDATE_NOTIF,
              dataObject: header.toJson());

      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      if (onSubmitted != null) onSubmitted();
      //FrameworkManager.getInstance().getDataManager().delete(InfoMessage.TABLE_NAME, InfoMessage.BE_LID + " = '" + header.getLid() + "'");
    } catch (e) {
      Logger.logError(className, 'submitNotificationInASync', e.toString());
    }
  }

  static Future<void> submitAllNotificationInAsync() async {
    List<NOTIF_HEADER> allNotifs =
        await NotificationHelper.getAllModifiedNotifications();

    for (NOTIF_HEADER notif in allNotifs) {
      await submitNotificationInASync(header: notif);
    }
  }

  static Future<void> submitAllNotificationInSync(
      {required BuildContext context, required Function onSubmitted}) async {
    List<NOTIF_HEADER> allNotifs =
        await NotificationHelper.getAllModifiedNotifications();

    for (NOTIF_HEADER notif in allNotifs) {
      await submitNotificationInSync(header: notif, context: context);
    }

    onSubmitted();
  }

  static Future<void> submitRoundsInAsync(
      {required ROUND_HEADER header}) async {
    try {
      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
              umpApplicationFunctionName: AppConstants.PA_SUBMIT_ROUND,
              dataObject: header.toJson());
    } catch (e) {
      Logger.logError(className, 'submitRoundsInAsync', e.toString());
    }
  }

  static Future<void> submitAllRoundsInAsync() async {
    // List<ORDER_HEADER> allOrders = await OrdersHelper.getAllModifiedOrders(
    //     meausrementContext: Constants.MEAS_CTX_ROUND);

    // for (ORDER_HEADER order in allOrders) {
    //   await submitRoundsInAsync(header: order);
    // }
  }

  static Future<void> submitAllRoundsInSync(
      {required BuildContext context, required Function onSubmitted}) async {
    // List<ORDER_HEADER> allOrders = await OrdersHelper.getAllModifiedOrders(
    //     meausrementContext: Constants.MEAS_CTX_ROUND);

    // for (ORDER_HEADER order in allOrders) {
    //   await submitRoundsInSync(
    //       header: order, context: context, onSubmitted: () {});
    // }
    // onSubmitted();
  }

  static Future<Result> getFormsInAsync() async {
    String userId = await Utils.getUserId();
    FORM_INPUT_HEADER form = FORM_INPUT_HEADER(language: "en", user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_DOWNLOAD_FORM_TEMPLATES,
            dataObject: {});
    return result;
  }

  static Future<Result> getAllNotificationsInAsync() async {
    String userId = await Utils.getUserId();
    NOTIF_INPUT_HEADER inputHeader = new NOTIF_INPUT_HEADER(user_id: userId);

    // inputHeader.language = await DbHelper.getLanguage();

    //SyncEngine.getInstance().sendDataRequest(Constants.PA_GET_NOTIFS, "");
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_NOTIFS,
            dataObject: {});

    return result;
  }

  static getAllNotificationsInSync() async {
    try {
      // NOTIF_INPUT_HEADER inputHeader = new NOTIF_INPUT_HEADER();
      // inputHeader.user_id = await Utils.getUserId();
      // inputHeader.language = await DbHelper.getLanguage();
      String userId = await Utils.getUserId();
      NOTIF_INPUT_HEADER inputHeader = new NOTIF_INPUT_HEADER(user_id: userId);
      inputHeader.user_id = await Utils.getUserId();

      //SyncEngine.getInstance().submitInSyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.PULL, inputHeader, null, Constants.PA_GET_NOTIFS, true, callBack);
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: AppConstants.PA_GET_NOTIFS,
              dataObject: inputHeader.toJson());
      return result;
    } catch (e) {
      Logger.logError(className, 'getAllNotificationsInSync', e.toString());
    }
  }

  static Future<Result> getAllTechObjectsInAsync() async {
    // return (await (SyncEngine()
    //       ..isAutoSave(true)
    //       ..isAsynchronous(true)
    //       ..setInputType(InputType.standard)
    //       ..setRequestType(RequestType.pull))
    //     .send(
    //         umpApplicationFunctionName:
    //             AppConstants.PA_GET_TECH_OBJECT_HIERARCHY,
    //         dataObject: {}));
    String userid = await Utils.getUserId();
    FUNC_LOC_INPUT_HEADER inputHeader =
        FUNC_LOC_INPUT_HEADER(fl_val: "", user_id: userid);
    Map<String, dynamic> data = {
      "FUNC_LOC_INPUT": [
        {FUNC_LOC_INPUT_HEADER.TABLE_NAME: inputHeader.toJson()}
      ]
    };
    var res = (await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(true)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_EXPLODE_FUNC_LOC,
            dataObject: data));
    print("getAllTechObjectsInAsync: $res");
    return res;
  }

  static getFunctionalLocationInSyncMode(
      {required FUNC_LOC_INPUT_HEADER functionalLocation}) async {
    Result result;
    try {
      functionalLocation.user_id = await Utils.getUserId();
      // functionalLocation.language = await DbHelper.getLanguage();
      Map<String, dynamic> data = {
        AppConstants.BE_FUNC_LOC_INPUT: [
          {FUNC_LOC_INPUT_HEADER.TABLE_NAME: functionalLocation.toJson()}
        ]
      };
      result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: AppConstants.PA_GET_FUNC_LOC,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(
          className, 'getFunctionalLocationInSyncMode', e.toString());
      throw e;
    }
  }

  static getEquipmentsInSyncMode(
      {required EQUIP_INPUT_HEADER equipInputHeader}) async {
    Result result;
    try {
      equipInputHeader.user_id = await Utils.getUserId();
      // equipInputHeader.language = await DbHelper.getLanguage();
      Map<String, dynamic> data = {
        AppConstants.BE_EQUIP_INPUT: [
          {EQUIP_INPUT_HEADER.TABLE_NAME: equipInputHeader.toJson()}
        ]
      };
      result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: AppConstants.PA_GET_EQUIPMENT,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(className, 'getEquipmentsInSyncMode', e.toString());
      throw e;
    }
  }

  static getMaterialsInSync(
      {required MATERIAL_INPUT_HEADER materialInputHeader}) async {
    try {
      materialInputHeader.user_id = await Utils.getUserId();
      // materialInputHeader.language = await DbHelper.getLanguage();
      // Map<String, dynamic> data = {
      //   AppConstants.BE_MATERIAL_INPUT: [
      //     {MATERIAL_INPUT_HEADER.TABLE_NAME: materialInputHeader.toJson()}
      //   ]
      // };
      Map<String, dynamic> data = {
        "MATERIAL_INPUT": [
          {
            "MATERIAL_INPUT_HEADER": {
              "USER_ID": materialInputHeader.user_id,
              "MAT_NO": materialInputHeader.mat_no,
            }
          }
        ]
      };
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..setInputType(InputType.custom)
            ..setRequestType(RequestType.pull))
          .send(
              umpApplicationFunctionName: AppConstants.PA_GET_MATERIALS,
              dataObject: data);
      return result;
    } catch (e) {
      Logger.logError(className, 'getEquipmentsInSyncMode', e.toString());
      throw e;
    }
  }

  static Future<Result> createNewOrderFromOrderTask(
      {required Map<String, dynamic> data}) async {
    // Result result = await (SyncEngine()
    //       ..isAutoSave(true)
    //       ..setInputType(InputType.custom)
    //       ..setRequestType(RequestType.query))
    //     .send(
    //         umpApplicationFunctionName: AppConstants.PA_CREATE_ORDER_TASKLIST,
    //         dataObject: data);
    // return result;
    return Result(404, {});
  }
  /*----------------------------------SYNC CALL------------------------------*/

  static Future<Result> getCustomizationInSyncMode() async {
    String userId = await Utils.getUserId();
    CUSTOMIZATION_INPUT_HEADER customization =
        CUSTOMIZATION_INPUT_HEADER(user_id: userId);
    final customizationFuture = (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(false)
          ..setInputType(InputType.custom)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_CUSTOMIZATION,
            dataObject: customization.toJson());
    final rigInfoFuture = getRigInfoInSyncMode();
    final results = await Future.wait([customizationFuture, rigInfoFuture]);
    Result result = results[0];
    return result;
  }

  static Future<Result> getRigInfoInSyncMode() async {
    String userId = await Utils.getUserId();
    var rigInput = {
      "RIG_INPUT": [
        {
          "RIG_INPUT_HEADER": {
            "EQUIP_NO": "",
            "RIGNO": "",
            "FUNC_LOC": "",
            "USER_ID": userId
          }
        }
      ]
    };

    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(false)
          ..setInputType(InputType.custom)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_RIG_INFO,
            dataObject: rigInput);
    return result;
  }

  static Future<Result?> submitOrderInSync(
      {required ORDER_HEADER header,
      required BuildContext context,
      required Function onSubmitted}) async {
    Result? result;
    UIHelper.showEamProgressDialog(
      context,
      title: header.objectStatus == ObjectStatus.add
          ? AppLocalizations.of(context)!.pleaseWaitWhileCreatingNewOrder
          : AppLocalizations.of(context)!.pleaseWaitWhileOrderIsUpdating,
      barrierDismissible: false,
      showCancelIcon: false,
    );
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DocumentHelper.getPendingDocuments(
              tableName: ORDER_DOCUMENT.TABLE_NAME, key: header.order_no!);
      documentHeaders = documentHeaders
          .where((documentHeader) =>
              documentHeader.objectStatus != ObjectStatus.global)
          .toList();
      List<DOCUMENT_HEADER> formdocumentHeaders =
          await DocumentHelper.getPendingFormDocuments(header.order_no!);

      formdocumentHeaders = formdocumentHeaders
          .where((formdocumentHeader) =>
              formdocumentHeader.objectStatus != ObjectStatus.global)
          .toList();

      List<FORM_OBJECT_HEADER> formObjects =
          await FormsHelper.fetchOrderFormObjectData(header.order_no!);

      formObjects = formObjects
          .where((formObject) => formObject.objectStatus != ObjectStatus.global)
          .toList();

      if (documentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in documentHeaders) {
          await submitDocumentsInSync(documentHeader: documentHeader);
        }
      }

      // Submit All Documents related forms
      if (formdocumentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in formdocumentHeaders) {
          await submitDocumentsInSync(documentHeader: documentHeader);
        }
      }

      // Submit All forms related Orders
      if (formObjects.isNotEmpty) {
        for (FORM_OBJECT_HEADER formObjectHeader in formObjects) {
          await submitFormsInSync(form_object_header: formObjectHeader);
        }
      }

      if (!Utils.isNullOrEmpty(header.notif_id)) {
        NOTIF_HEADER? notifHeader =
            await NotificationHelper.getNotifHeader(notifNo: header.notif_id);

        if (notifHeader != null) {
          if (notifHeader.objectStatus != ObjectStatus.global) {
            await submitNotificationInSync(
              header: notifHeader,
            );

            //FrameworkManager.getInstance().getDataManager().delete(InfoMessage.TABLE_NAME, InfoMessage.BE_LID + " = '" + notifHeader.getLid() + "'");
          }
        }
      }
      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      if (header.objectStatus == ObjectStatus.add) {
        //SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, null, Constants.PA_CREATE_ORDER, header.getBEName(), header.getLid(), false);
        result = await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(false)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: AppConstants.PA_CREATE_ORDER,
                dataObject: header.toJson());
      } else {
        // SyncEngine.getInstance().submitInAsyncMode(SyncConstants.MESSAGE_REQUEST_TYPE.RQST, header, null, Constants.PA_UPDATE_ORDER, header.getBEName(), header.getLid(), false);
        result = await (SyncEngine()
              ..isAutoSave(true)
              ..isAsynchronous(false)
              ..setInputType(InputType.standard)
              ..setRequestType(RequestType.rqst))
            .send(
                umpApplicationFunctionName: AppConstants.PA_UPDATE_ORDER,
                dataObject: header.toJson());
        context.read<OrderNotifier>().getOrders(context);

        debugPrint(result.body);
      }

      //Close dialog
      onSubmitted();
    } catch (e) {
      Logger.logError("PAHelper", "submitOrderInASync", e.toString());
    }
    UIHelper.closeDialog(context);

    return result;
  }

  static Future<void> submitDocumentsInSync(
      {required DOCUMENT_HEADER documentHeader}) async {
    try {
      // await (SyncEngine()
      //       ..isAutoSave(true)
      //       ..isAsynchronous(false)
      //       ..setInputType(InputType.standard)
      //       ..setRequestType(RequestType.rqst))
      //     .send(
      //         umpApplicationFunctionName: AppConstants.PA_MODIFY_DOCUMENT,
      //         dataObject: documentHeader.toJson());
    } catch (e) {
      Logger.logError(className, 'submitDocumentsInAsync', e.toString());
    }
  }

  //Submit notification header in sync mode
  static Future<void> submitNotificationInSync(
      {required NOTIF_HEADER header,
      BuildContext? context,
      Function? onSubmitted}) async {
    if (context != null) {
      UIHelper.showEamProgressDialog(
        context,
        title: header.objectStatus == ObjectStatus.add
            ? AppLocalizations.of(context)!
                .pleaseWaitWhileCreatingNewNotification
            : AppLocalizations.of(context)!
                .pleaseWaitWhileNotificationIsUpdating,
        barrierDismissible: false,
        showCancelIcon: false,
      );
    }
    try {
      List<DOCUMENT_HEADER> documentHeaders =
          await DocumentHelper.getPendingDocuments(
              tableName: NOTIF_DOCUMENT.TABLE_NAME, key: header.notif_no!);

      documentHeaders = documentHeaders
          .where((documentHeader) =>
              documentHeader.objectStatus != ObjectStatus.global)
          .toList();

      List<FORM_OBJECT_HEADER> formObjects =
          await FormsHelper.fetchNotificationFormObjectData(header.notif_no!);

      formObjects = formObjects
          .where((formObject) => formObject.objectStatus != ObjectStatus.global)
          .toList();

      if (documentHeaders.isNotEmpty) {
        for (DOCUMENT_HEADER documentHeader in documentHeaders) {
          await submitDocumentsInSync(documentHeader: documentHeader);
        }
      }

      // Submit All forms related Orders
      if (formObjects.isNotEmpty) {
        for (FORM_OBJECT_HEADER formObjectHeader in formObjects) {
          await submitFormsInSync(form_object_header: formObjectHeader);
        }
      }

      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
              umpApplicationFunctionName: Constants.P_MODE_ADD == header.p_mode
                  ? AppConstants.PA_CREATE_NOTIF
                  : AppConstants.PA_UPDATE_NOTIF,
              dataObject: header.toJson());

      if (onSubmitted != null) onSubmitted();
    } catch (e) {
      Logger.logError(className, 'submitNotificationInASync', e.toString());
    }

    if (context != null) UIHelper.closeDialog(context);
    ;
  }

  //Submit round in sync mode
  static Future<void> submitRoundsInSync(
      {required ROUND_HEADER header,
      required BuildContext context,
      required Function onSubmitted}) async {
    UIHelper.showEamProgressDialog(
      context,
      title: AppLocalizations.of(context)!.pleaseWaitWhileInspectionIsUpdating,
      barrierDismissible: false,
      showCancelIcon: false,
    );

    try {
      await InfoMessageHelper().deleteInfoMessageByBeLid(header.lid);
      Result result = await (SyncEngine()
            ..isAutoSave(true)
            ..isAsynchronous(false)
            ..setInputType(InputType.standard)
            ..setRequestType(RequestType.rqst))
          .send(
              umpApplicationFunctionName: AppConstants.PA_SUBMIT_ROUND,
              dataObject: header.toJson());
    } catch (e) {
      Logger.logError(className, 'submitRoundsInSync', e.toString());
    }
    UIHelper.closeDialog(context);
    onSubmitted();
  }

//Download forms in sync mode
  static Future<Result> getFormsInSync() async {
    // String userId = await Utils.getUserId();
    // FORM_INPUT_HEADER form = FORM_INPUT_HEADER(language: "en", user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(false)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_DOWNLOAD_FORM_TEMPLATES,
            dataObject: {});
    return result;
  }

//Download rounds in sync mode
  static Future<Result> getAllRoundsInSync() async {
    String userId = await Utils.getUserId();
    ORDER_INPUT_HEADER order = ORDER_INPUT_HEADER(user_id: userId);
    Result result = await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(false)
          ..setInputType(InputType.standard)
          ..setRequestType(RequestType.pull))
        .send(
            umpApplicationFunctionName: AppConstants.PA_GET_ROUNDS,
            dataObject: order.toJson());
    return result;
  }

//Download techobject in sync mode
  static Future<Result> getAllTechObjectsInSync() async {
    // return (await (SyncEngine()
    //       ..isAutoSave(true)
    //       ..isAsynchronous(false)
    //       ..setInputType(InputType.standard)
    //       ..setRequestType(RequestType.pull))
    //     .send(
    //         umpApplicationFunctionName:
    //             AppConstants.PA_GET_TECH_OBJECT_HIERARCHY,
    //         dataObject: {}));
    return Result(404, {});
  }

  // Get MAP Settings
  static Future<Result> getMapSettingsInSync() async {
    String userId = await Utils.getUserId();
    //TODO : language has to be taken from preference
    Map<String, dynamic> input = {"language": "en", "user_id": userId};

    // Result result = await (SyncEngine()
    //       ..isAutoSave(false)
    //       ..setInputType(InputType.custom)
    //       ..setRequestType(RequestType.pull))
    //     .send(
    //         umpApplicationFunctionName: AppConstants.PA_GET_MAP_SETTINGS,
    //         dataObject: input);

    // return result;
    return Result(404, {});
  }

  static Future<void> doRefresh() async {
    // if (await SettingsHelper().getSentItemsCount() > 0) {
    SyncEngine().receive();
    // }
  }

  static getAllOrderIdeal() async {
    await requestDataDownload(AppConstants.PA_GET_ORDERS, null);
  }

  static getAllNotificationsIdeal() async {
    await requestDataDownload(AppConstants.PA_GET_NOTIFS, null);
  }

  static getAllTechObjectIdeal() async {
    // await requestDataDownload(AppConstants.PA_GET_TECH_OBJECT_HIERARCHY, null);
  }

  static getAllFormIdeal() async {
    await requestDataDownload(AppConstants.PA_DOWNLOAD_FORM_TEMPLATES, null);
  }

  static getAllRoundsIdeal() async {
    await requestDataDownload(AppConstants.PA_GET_ROUNDS, null);
  }

  static Future<void> requestDataDownload(
      String functionName, dynamic input) async {
    await SettingsHelper().requestInitialDataDownload(functions: [
      {"name": functionName, "input": input}
    ]);
  }

  static Future<Result> createRoundInSync(
      {required String characteristic, required String rigNo}) async {
    String userId = await Utils.getUserId();

    ROUND_INPUT_HEADER round_input_header = ROUND_INPUT_HEADER(
        characteristic: int.parse(characteristic),
        rig_no: rigNo,
        user_id: userId);

    Result result = (await (SyncEngine()
          ..isAutoSave(true)
          ..isAsynchronous(false)
          ..setInputType(InputType.custom)
          ..setRequestType(RequestType.query))
        .send(
            umpApplicationFunctionName: AppConstants.PA_CREATE_ROUND,
            dataObject: {
          "ROUND_INPUT": [
            {
              "ROUND_INPUT_HEADER": {
                "USER_ID": round_input_header.user_id,
                "CHARACTERISTIC": round_input_header.characteristic.toString(),
                "RIG_NO": round_input_header.rig_no
              }
            }
          ]
        }));
    return result;
  }
}
