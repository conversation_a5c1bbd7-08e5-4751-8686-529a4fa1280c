import 'package:eam/helpers/application_helper.dart';
import 'package:eam/helpers/pa_helper.dart';
import 'package:eam/helpers/tech_objects_helper.dart';
import 'package:eam/helpers/ui_helper.dart';
import 'package:eam/models/menu_action_dropdown.dart';
import 'package:eam/presentation/widgets/atoms_layer/eam_icons.dart';
import 'package:eam/provider/assistant/FabVisibilityProvider.dart';
import 'package:eam/provider/tech_objects/equipment_provider.dart';
import 'package:eam/provider/tech_objects/floc_provider.dart';
import 'package:eam/presentation/pages/tech_objects/equipments/equipments_page.dart';
import 'package:eam/presentation/pages/tech_objects/equipments/search_equipment.dart';
import 'package:eam/presentation/pages/tech_objects/floc/func_loc_page.dart';
import 'package:eam/presentation/pages/tech_objects/floc/search_floc.dart';
import 'package:eam/presentation/pages/tech_objects/tech_object_hierarchy_page.dart';
import 'package:eam/presentation/pages/tech_objects/tech_object_treeview.dart';
import 'package:eam/utils/app_constants.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/widgets/eam_switch2.dart';
import 'package:eam/widgets/menu_action_button.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:provider/provider.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../../../helpers/platform_details.dart';
import '../../common_widgets/eam_icon_button.dart';
import '../../../services/navigation_service.dart';
import '../../../utils/app_dimension.dart';
import '../../../widgets/eam_toolbar2.dart';

class TechObjectsPage extends StatefulWidget {
  static const routeName = "/tech-objects";

  const TechObjectsPage({Key? key}) : super(key: key);

  @override
  _TechObjectsPageState createState() => _TechObjectsPageState();
}

class _TechObjectsPageState extends State<TechObjectsPage> {
  final PageStorageBucket bucket = PageStorageBucket();
  int selectedIndex = 0;
  List<Widget> bodyContentList = [];
  IconData actionIcon = FontAwesomeIcons.magnifyingGlass;
  late FlocProvider _flocProvider;
  late EquipmentProvider _equipmentProvider;
  late TextEditingController searchTextController;

  @override
  void initState() {
    searchTextController = TextEditingController();
    // PAHelper.doRefresh();
    super.initState();
    bodyContentList = [
      FuncLocPage(
        key: PageStorageKey('FLOC'),
      ),
      EquipmentsPage(
        key: PageStorageKey('Equipment'),
      ),
    ];
  }

  double screenWidth = 0.0;
  double screenHeight = 0.0;

  void initProvider() {
    var flocProvider = Provider.of<FlocProvider>(context, listen: false);

    flocProvider.getFunctionalLocations();
    var equipProvider = Provider.of<EquipmentProvider>(context, listen: false);
    equipProvider.loadEquipmentList();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateFabVisibility("Functional Location");
  }

  void _updateFabVisibility(String? contextMessage) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<FabVisibilityProvider>(context, listen: false)
          .updateContext(contextMessage);
    });
  }

  _getAppBarTitle() {
    final String count = (selectedIndex == 0)
        ? _flocProvider.filteredList.length.toString()
        : _equipmentProvider.filteredList.length.toString();

    if (selectedIndex == 0) {
      return _flocProvider.functionLocationCount != null
          ? AppLocalizations.of(context)!.floc + " (${count})"
          : AppLocalizations.of(context)!.floc;
    } else {
      return _equipmentProvider.equipmentsCount != null
          ? AppLocalizations.of(context)!.equipment + " (${count})"
          : AppLocalizations.of(context)!.equipment;
    }
  }

  List<Widget> _getActionButton() {
    return [
      _techObjectsSwitch(),
      if (!PlatformDetails.isMobileScreen(context)) ...[
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: EamIconButton(
              title: AppLocalizations.of(context)!.viewHierarchy,
              onTap: () {
                _navigateToHierarchy();
              },
              icon: EamIcon(iconName: EamIcon.hierarchy)),
        ),
      ],
      MenuActionButton(
          options: [
            MenuActionItem(
              value: 'get_all_tech_objects',
              name: AppLocalizations.of(context)!.getAllTechObjects,
            ),
            MenuActionItem(
              value: 'delete_all',
              name: AppLocalizations.of(context)!.deleteAllString,
            ),
            MenuActionItem(
              value: 'search',
              name: AppLocalizations.of(context)!.searchString,
            ),
            MenuActionItem(
              value: 'refresh',
              name: AppLocalizations.of(context)!.refreshString,
            ),
            if (PlatformDetails.isMobileScreen(context)) ...[
              MenuActionItem(
                value: 'hierarchy',
                name: AppLocalizations.of(context)!.hierarchy,
              ),
            ],
          ],
          onOptionItemSelected: (item) async {
            switch (item.value) {
              case 'get_all_tech_objects':
                await _getTechObjectFromUMP();
                break;
              case 'delete_all':
                _deleteAllFunctionalLocations();
                break;
              case 'search':
                if (selectedIndex == 0) {
                  _navigateToFuncLocationSearch();
                } else if (selectedIndex == 1) {
                  _navigateToEquipmentSearch();
                }
                setState(() {});
                break;
              case 'hierarchy':
                _navigateToHierarchy();
                break;
              case 'refresh':
                await SyncEngine().receive();
                break;
            }
          })
    ];
  }

  _navigateToHierarchy() {
    _navigateToHierarchy2();
    // Navigator.pushNamed(context, TechObjectsHierarchyPage.routeName,
    //     arguments: {
    //       TechObjectsHierarchyPage.MODE: selectedIndex == 0
    //           ? TechObjectsHierarchyPage.FUNCTIONAL_LIST_MODE
    //           : TechObjectsHierarchyPage.EQUIPMENT_MODE,
    //     });
  }

  _navigateToHierarchy2() {
    NavigationService.pushNamed(TechObjectTreeView.routeName, arguments: {
      TechObjectTreeView.MODE: selectedIndex == 0
          ? TechObjectTreeView.FUNCTIONAL_LIST_MODE
          : TechObjectTreeView.EQUIPMENT_LIST_MODE,
    });
  }

  _navigateToEquipmentSearch() {
    NavigationService.pushNamed(
      SearchEquipmentPage.routeName,
    ).then(
      (value) {
        if (value is bool && value == true) {
          Provider.of<EquipmentProvider>(context, listen: false)
              .loadEquipmentList();
        }
      },
    );
  }

  _navigateToFuncLocationSearch() {
    NavigationService.pushNamed(
      SearchFuncLocationPage.routeName,
    ).then(
      (value) {
        if (value is bool && value == true) {
          Provider.of<FlocProvider>(context, listen: false)
              .getFunctionalLocations();
        }
      },
    );
  }

  _techObjectsSwitch() {
    return EamToggleWidget(
      firstIcon: EamIcon(iconName: EamIcon.location),
      secondIcon: EamIcon(iconName: EamIcon.equip),
      onToggleCallback: (value) {
        setState(() {
          selectedIndex = value;
        });
        _onSearchValueChange(searchTextController.text);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    _flocProvider = Provider.of<FlocProvider>(context);
    _equipmentProvider = Provider.of<EquipmentProvider>(context);

    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
      systemNavigationBarColor: Colors.white,
    ));

    return Scaffold(
      appBar: _getAppBar(),
      body: Padding(
        padding: EdgeInsets.all(0.0),
        child: Column(
          children: [
            Expanded(
              child: IndexedStack(
                children: bodyContentList,
                index: selectedIndex,
              ),
            ),
          ],
        ),
      ),
    );
  }

  _getAppBar() {
    return EamAppBar(
      context: context,
      enableBack: PlatformDetails.isMobileScreen(context),
      title: _getAppBarTitle(),
      actionButton: _getActionButton(),
      searchRequired: true,
      onScanned: _onScanned,
      onSearchValueChange: _onSearchValueChange,
    );
  }

  Future<void> _getTechObjectFromUMP() async {
    String mode = !kIsWeb
        ? Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND
        : Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND;
    // if (mode ==
    //     (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND)) {
    //   bool _isCheck = await ApplicationHelper()
    //       .isDeviceSetting(key: AppConstants.PA_GET_TECH_OBJECT_HIERARCHY);
    //   if (_isCheck) {
    //     _techRequestCheck();
    //   } else {
    //     await ApplicationHelper.addDeviceSetting(
    //         key: AppConstants.PA_GET_TECH_OBJECT_HIERARCHY,
    //         value: Constants.YES);
    //     await PAHelper.getAllTechObjectIdeal();

    //     UIHelper.showSnackBar(context,
    //         message: AppLocalizations.of(context)!.fetchingTechObjects);
    //   }
    // } else if (mode ==
    //     (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND)) {
    //   bool _isCheck = await ApplicationHelper()
    //       .isDeviceSetting(key: AppConstants.PA_GET_TECH_OBJECT_HIERARCHY);
    //   if (_isCheck) {
    //     _techRequestCheck();
    //   } else {
    //     await ApplicationHelper.addDeviceSetting(
    //         key: AppConstants.PA_GET_TECH_OBJECT_HIERARCHY,
    //         value: Constants.YES);
    //     await getTechObjectInSync();
    //   }
    // }
    if(mode == Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND){
      await PAHelper.getAllTechObjectsInAsync();
    }else{
      await PAHelper.getAllTechObjectsInSync();
    }
  }

  _techObjectDownload() async {
    String mode = !kIsWeb
        ? Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND
        : Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND;
    if (mode ==
        (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_BACKGROUND)) {
      await PAHelper.getAllTechObjectIdeal();

      UIHelper.showSnackBar(context,
          message: AppLocalizations.of(context)!.fetchingTechObjects);
    } else if (mode ==
        (Constants.OPTIONS_FIELD_NAME_EXECUTION_MODE_VALUE_FOREGROUND)) {
      await getTechObjectInSync();
    }
  }

  Future<void> _techRequestCheck() async {
    UIHelper.showEamDialog2(
      context,
      positiveActionLabel: AppLocalizations.of(context)!.yesString,
      negativeActionLabel: AppLocalizations.of(context)!.cancel,
      title: AppLocalizations.of(context)!.alertString,
      description:
          "Request already queued. Do you want to submit another request?",
      onPositiveClickListener: () async {
        // Future.microtask(() => FormsHelper.deleteAllForms()).then((value) {
        //   _initProvider();
        //   UIHelper.closeDialog(context);
        // });
        //  await ApplicationHelper.deleteDeviceSetting(
        //   key: AppConstants.PA_GET_TECH_OBJECT_HIERARCHY,
        //   value: Constants.YES);
        UIHelper.closeDialog(context);
        _techObjectDownload();
      },
      onNegativeClickListener: () =>
          Navigator.of(context, rootNavigator: true).pop(),
    );
  }

  Future<void> getTechObjectInSync() async {
    bool isPopped = false;

    try {
      UIHelper.showEamProgressDialog(
        context,
        title:
            AppLocalizations.of(context)!.pleaseWaitWhileDownloadingTechObject,
      );
      Result result = await PAHelper.getAllTechObjectsInSync();
      Navigator.of(context, rootNavigator: true).pop();
      isPopped = true;

      if (result.statusCode == Status.httpOk ||
          result.statusCode == Status.httpCreated) {
        UIHelper.showSnackBar(
          context,
          message: AppLocalizations.of(context)!.techObjectDownloadSuccessfully,
        );
        initProvider();
      }
    } catch (e) {
      if (!isPopped) Navigator.of(context, rootNavigator: true).pop();
      UIHelper.showSnackBar(context, message: e.toString());
    }
    return;
  }

  _onScanned(result) {
    searchTextController.text = result;

    _flocProvider.filter(
      searchString: searchTextController.text.toLowerCase(),
    );

    _equipmentProvider.filter(
      searchString: searchTextController.text.toLowerCase(),
    );
  }

  _onSearchValueChange(res) {
    searchTextController.text = res;
    if (selectedIndex == 0) {
      _flocProvider.filter(searchString: res.toLowerCase());
    } else {
      _equipmentProvider.filter(searchString: res.toLowerCase());
    }
  }

  double searchBarWidth() {
    if (screenWidth <= 800) {
      return double.maxFinite;
    } else if (screenWidth <= 1000) {
      return double.maxFinite;
    }
    return Dimensions.searchControllerMaxWidth;
  }
  
  void _deleteAllFunctionalLocations() {
    String description;
    if (selectedIndex == 0) {
      description = "Are you sure you want to delete all Functional Locations?";
    } else {
      description = "Are you sure you want to delete all Equipments?";
    }
    UIHelper.showEamDialog2(
      context,
      positiveActionLabel: AppLocalizations.of(context)!.okayString,
      negativeActionLabel: AppLocalizations.of(context)!.cancel,
      title: AppLocalizations.of(context)!.alertString,
      description: description,
      onPositiveClickListener: () async {
        Navigator.of(context, rootNavigator: true).pop();
        if (selectedIndex == 0) {
          await TechObjectsHelper.deleteAllFunctionalLocations();
        } else {
          await TechObjectsHelper.deleteAllEquipments();
        }
        initProvider();
      },
      onNegativeClickListener: () =>
          Navigator.of(context, rootNavigator: true).pop(),
    );
  }
}
