import 'dart:convert';

import 'package:eam/be/C_CAT_PROFILE_HEADER.dart';
import 'package:eam/be/C_CODE_GROUP_HEADER.dart';
import 'package:eam/be/C_EFFECT_HEADER.dart';
import 'package:eam/be/C_NOTIF_TYPE_DETAILS_HEADER.dart';
import 'package:eam/be/C_NOTIF_TYPE_HEADER.dart';
import 'package:eam/be/C_PRIORITY_HEADER.dart';
import 'package:eam/be/C_SYSTEM_AVAIL_COND_HEADER.dart';
import 'package:eam/be/C_USER_STATUS_HEADER.dart';
import 'package:eam/be/EQUIP_HEADER.dart';
import 'package:eam/be/FUNC_LOC_HEADER.dart';
import 'package:eam/be/NOTIF_ACCT_ASGNMNT.dart';
import 'package:eam/be/NOTIF_ACTION.dart';
import 'package:eam/be/NOTIF_ACTIVITY.dart';
import 'package:eam/be/NOTIF_CAUSE.dart';
import 'package:eam/be/NOTIF_CUST_ADD.dart';
import 'package:eam/be/NOTIF_DOCUMENT.dart';
import 'package:eam/be/NOTIF_HEADER.dart';
import 'package:eam/be/NOTIF_ITEM.dart';
import 'package:eam/be/NOTIF_LOC_DATA.dart';
import 'package:eam/be/NOTIF_LONG_TEXT_ADD.dart';
import 'package:eam/be/NOTIF_LONG_TEXT_VIEW.dart';
import 'package:eam/be/NOTIF_PARTNER.dart';
import 'package:eam/be/NOTIF_TASK.dart';
import 'package:eam/be/NOTIF_USER_STATUS.dart';
import 'package:eam/be/ORDER_HEADER.dart';
import 'package:eam/be/RIG_HEADER.dart';
import 'package:eam/helpers/application_helper.dart';
import 'package:eam/helpers/db_helper.dart';
import 'package:eam/helpers/document_helper.dart';
import 'package:eam/helpers/equipment_helper.dart';
import 'package:eam/helpers/forms_helper.dart';
import 'package:eam/helpers/functional_location_helper.dart';
import 'package:eam/helpers/tech_objects_helper.dart';
import 'package:eam/models/generic_ron_model.dart';
import 'package:eam/models/home/<USER>';
import 'package:eam/models/notification/notif_activity_model.dart';
import 'package:eam/models/notification/notif_task_model.dart';
import 'package:eam/models/notification/notification_item_model.dart';
import 'package:eam/models/notification/notification_model.dart';
import 'package:eam/models/orders/selected_model.dart';
import 'package:eam/provider/notification/item/notification_item_list_provider.dart';
import 'package:eam/utils/constants.dart';
import 'package:eam/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:logger/logger.dart';
import 'package:sprintf/sprintf.dart';
import 'package:unvired_sdk/unvired_sdk.dart';

import '../be/NOTIF_FORM.dart';

class NotificationHelper {
  static const String className = "NotificationHelper";

  static Future<int> getNotifTotalCount() async {
    String whereClause = NOTIF_HEADER.FIELD_HISTORY_FLAG +
        " IS NULL OR " +
        NOTIF_HEADER.FIELD_HISTORY_FLAG +
        " = '' ";

    var query =
        "SELECT COUNT(*) AS count FROM ${NOTIF_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return int.parse(dataList[0]["count"]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotifTotalCount', e.toString());
    }
    return 0;
  }

  static Future<int> getNotifLocalCount() async {
    String whereClause = "(" +
        FieldObjectStatus +
        " = '" +
        ObjectStatus.add.toString() +
        "' OR " +
        NOTIF_HEADER.FIELD_ORDR_ID +
        " IS NULL OR " +
        NOTIF_HEADER.FIELD_ORDR_ID +
        " = '') AND (" +
        NOTIF_HEADER.FIELD_HISTORY_FLAG +
        " IS NULL OR " +
        NOTIF_HEADER.FIELD_HISTORY_FLAG +
        " = '') ";

    var query =
        "SELECT COUNT(*) AS count FROM ${NOTIF_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return int.parse(dataList[0]["count"]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotifLocalCount', e.toString());
    }
    return 0;
  }

  static Future<NOTIF_HEADER?> getNotifHeader({final String? notifNo}) async {
    if (notifNo == null || notifNo.isEmpty) {
      return null;
    }
    try {
      final String whereClause =
          NOTIF_HEADER.FIELD_NOTIF_NO + " = '" + notifNo + "'";

      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotifHeader', e.toString());
    }
    return null;
  }

  static Future<void> deleteAllNotifications() async {
    try {
      final String commonWhereClause =
          "${FieldSyncStatus} = ${SyncStatus.none.index} AND ${FieldObjectStatus} = ${ObjectStatus.global.index} AND " +
              "(" +
              NOTIF_HEADER.FIELD_HISTORY_FLAG +
              " IS NULL OR " +
              NOTIF_HEADER.FIELD_HISTORY_FLAG +
              " = '')";

      await AppDatabaseManager().execute('DELETE FROM ' +
          NOTIF_HEADER.TABLE_NAME +
          ' WHERE ' +
          commonWhereClause);

      // Deleting all orphaned documents and forms
      await DocumentHelper.cleanupDocuments();
      await FormsHelper.cleanupFormObjectHeaders();
      // Delting all Info messages relevent notifications
      await InfoMessageHelper()
          .deleteInfoMessageByBeName(NOTIF_HEADER.TABLE_NAME);
    } catch (e) {
      Logger.logError(className, 'deleteAllNotifications', e.toString());
    }
  }

  Future<List<NOTIF_HEADER>> getAllNotificationHeaders() async {
    List<NOTIF_HEADER> notifList = [];
    try {
      List<dynamic> dataList = await AppDatabaseManager()
          .select(DBInputEntity(NOTIF_HEADER.TABLE_NAME, {}));
      if (dataList.length > 0) {
        for (var data in dataList) {
          notifList.add(NOTIF_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getAllNotificationHeaders', e.toString());
    }
    return notifList;
  }

  /// Implementation of getNotificationsViewModels
  static Future<List<GenericRONModel>> getNotificationsModelList(
      {required BuildContext context, String? where}) async {
    //String? roundsOrderType = (await ApplicationHelper.getRoundsOrderType());

    if (where == null) {
      /*where = "(" +
          FieldObjectStatus +
          " = '" +
          ObjectStatus.modify.index.toString() +
          "' OR " +
          NOTIF_HEADER.FIELD_ORDR_ID +
          " IS NULL OR " +
          NOTIF_HEADER.FIELD_ORDR_ID +
          " = '') AND (" +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " IS NULL OR " +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " = '') ";*/
      where = "(" +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " IS NULL OR " +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " = '')";
    }

    List<GenericRONModel> models = [];
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
            ..setWhereClause(where +
                " ORDER BY SYNC_STATUS DESC,OBJECT_STATUS DESC," +
                NOTIF_HEADER.FIELD_REQ_START_DAT +
                " ASC"));
      if (dataList.length > 0) {
        for (var data in dataList) {
          models.add(
            (await getNotificationModel(
                notif_header: NOTIF_HEADER.fromJson(data), context: context)),
          );
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationsModelList', e.toString());
    }

    return models;
  }

  static Future<List<GenericRONModel>> getNotificationsByNumbers(
      {required BuildContext context,
      required List<String> notifNumbers}) async {
    if (notifNumbers.isEmpty) {
      return [];
    }

    String whereClause =
        "${NOTIF_HEADER.FIELD_NOTIF_NO} IN ('${notifNumbers.join("','")}')";

    List<GenericRONModel> models = [];
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
        DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
          ..setWhereClause(whereClause +
              " ORDER BY SYNC_STATUS DESC,OBJECT_STATUS DESC," +
              NOTIF_HEADER.FIELD_REQ_START_DAT +
              " ASC"),
      );

      if (dataList.isNotEmpty) {
        models = await Future.wait(dataList.map((data) async {
          return await getNotificationModel(
              notif_header: NOTIF_HEADER.fromJson(data), context: context);
        }));
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationsByNumbers', e.toString());
    }

    return models;
  }

  static Future<List<GenericRONModel>> getFilteredExistingModels(
      {required BuildContext context,
      required List<GenericRONModel> existingModels,
      String? where}) async {
    List<GenericRONModel> models = [];

    if (where == null) {
      where = "(" +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " IS NULL OR " +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " = '')";
    }

    try {
      // Fetch filtered data using where condition
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
            ..setWhereClause(where +
                " ORDER BY SYNC_STATUS DESC,OBJECT_STATUS DESC," +
                NOTIF_HEADER.FIELD_REQ_START_DAT +
                " ASC"));

      List<GenericRONModel> filteredModels = [];

      if (dataList.isNotEmpty) {
        for (var data in dataList) {
          filteredModels.add(
            await getNotificationModel(
                notif_header: NOTIF_HEADER.fromJson(data), context: context),
          );
        }
      }

      // Compare filtered models with existing models
      for (var filteredModel in filteredModels) {
        if (existingModels
            .any((model) => model.getKey == filteredModel.getKey)) {
          models.add(filteredModel);
        }
      }
    } catch (e) {
      Logger.logError(
          'getFilteredExistingModels', 'Error filtering models', e.toString());
    }

    return models;
  }

//IMPLEMENTATION OF getViewModel
  static Future<GenericRONModel> getNotificationModel(
      {required NOTIF_HEADER notif_header,
      required BuildContext context}) async {
    GenericRONModel viewModel = GenericRONModel(key: notif_header.notif_no!);
    viewModel.syncStatus = notif_header.syncStatus;
    viewModel.title = notif_header.short_text ?? "";
    viewModel.systemStatus = notif_header.syst_stat ?? "";
    viewModel.userStatus = notif_header.user_status ?? "";
    viewModel.infoMsgCat = notif_header.infoMsgCat ?? "";
    if (notif_header.req_start_dat != null) {
      viewModel.date = Utils.getDateInDeviceFormat(
          timestamp:
              Utils.getTimestampFromServerDate(notif_header.req_start_dat!));
    }
    if (notif_header.req_end_dat != null) {
      viewModel.endDate = Utils.getDateInDeviceFormat(
          timestamp:
              Utils.getTimestampFromServerDate(notif_header.req_end_dat!));
    }

    if (!Utils.isNullOrEmpty(notif_header.priority)) {
      C_PRIORITY_HEADER? header = await ApplicationHelper.getPriorityHeader(
          priority: notif_header.priority!);
      if (header != null) {
        viewModel.priority = header.priority!;
        viewModel.priorityDesc = header.priority_desc!;
      }
    }
    viewModel.subTitle1 = notif_header.notif_no!;
    viewModel.subTitleIcon1 = FontAwesomeIcons.bell;

    viewModel.subTitle2 = notif_header.ordr_id;
    viewModel.subTitleIcon2 = Icons.attach_money;

//		viewModel.getSubTitle3(notif_header.getEQUIP_DESC());

    viewModel.subTitle3 =
        notif_header.notif_type! + ' - ' + notif_header.notif_type_text!;
    // viewModel.functionalLocation = notif_header.func_loc_desc!;
    viewModel.functionalLocation =
        "${notif_header.func_loc_id!} ${notif_header.func_loc_desc!.isEmpty ? "" : "(${notif_header.func_loc_desc!})"}";
    viewModel.type = notif_header.notif_type_text ?? '';
    if (!Utils.isNullOrEmpty(notif_header.equip_id)) {
      viewModel.equipmentName = notif_header.equip_desc ?? "";
      viewModel.equipmentNumber = notif_header.equip_id ?? "";
    }
    if (!Utils.isNullOrEmpty(notif_header.ordr_id)) {
      viewModel.orderNo = notif_header.ordr_id ?? "";
    }
    switch (notif_header.syncStatus) {
      case SyncStatus.error:
        viewModel.status = Colors.red;
        await _deleteNotificationAction(notifHeader: notif_header);
        break;
      case SyncStatus.none:
        viewModel.status = Colors.transparent;

        if (notif_header.objectStatus == ObjectStatus.add ||
            notif_header.objectStatus == ObjectStatus.modify) {
          viewModel.status = Theme.of(context).primaryColor;
        }

        viewModel.isCompleted =
            ((!Utils.isNullOrEmpty(notif_header.syst_stat) &&
                    notif_header.syst_stat!.contains(Constants.NOCO)) ||
                await isNotifActionPresent(notifNo: notif_header.notif_no!));

        break;
      case SyncStatus.sent:
      case SyncStatus.queued:
        viewModel.status = Colors.orange;
        viewModel.isCompleted = (!Utils.isNullOrEmpty(notif_header.syst_stat) &&
                notif_header.syst_stat!.contains(Constants.NOCO)) ||
            await isNotifActionPresent(notifNo: notif_header.notif_no!);
        break;
    }

    if (notif_header.objectStatus == ObjectStatus.add ||
        notif_header.objectStatus == ObjectStatus.modify) {
      try {
        viewModel.isSubmitRequired =
            !(await SettingsHelper().isInOutBoxQueue(notif_header.lid)) &&
                !(await SettingsHelper().isInSentItems(notif_header.lid));
      } catch (e) {
        //Logger.e(e.getMessage());
        viewModel.isSubmitRequired = false;
      }
    } else {
      viewModel.isSubmitRequired = false;
    }

    viewModel.reportedBy = notif_header.reprtd_by;
    viewModel.assgnedTo = notif_header.user_responsible;

    viewModel.mainWorkCntr = notif_header.main_work_cntr;
    viewModel.mainWorkCntrDesc = notif_header.main_work_cntr_desc;

    return viewModel;
  }

  //TODO
  //static List<MultiSelectViewModel> getNotificationTypesModels() {}

  //TODO
  //static List<MultiSelectViewModel> getNotificationPriorityModels() {}

  //TODO
  //static GenericRONViewModel getViewModel(NOTIF_HEADER notifHeader) {}

  //IMPLEMENTAION OF getNotificationViewModel
  static Future<NotificationModel> getNotificationViewModel(
      {required NOTIF_HEADER notifHeader,
      required NotificationModel notificationModel}) async {
    notificationModel.notificationNo = notifHeader.notif_no ?? "";
    notificationModel.breakdownDuration = notifHeader.breakdwn_durtn.toString();
    notificationModel.breakdownDurationUnit =
        notifHeader.breakdwn_durtn_unit ?? "";
    if (!Utils.isNullOrEmpty(notifHeader.ordr_id)) {
      notificationModel.orderNo = notifHeader.ordr_id!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.short_text)) {
      notificationModel.shortText = notifHeader.short_text!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.notif_type)) {
      notificationModel.notificationType = notifHeader.notif_type!;
      notificationModel.notificationTypeDesc = notifHeader.notif_type_text!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.priority)) {
      C_PRIORITY_HEADER? header = await ApplicationHelper.getPriorityHeader(
          priority: notifHeader.priority!);
      if (header != null) {
        notificationModel.priority = notifHeader.priority!;
        notificationModel.priorityDesc = notifHeader.priority_desc!;
      }
    }

    if (!Utils.isNullOrEmpty(notifHeader.equip_id)) {
      notificationModel.equipment = notifHeader.equip_id!;
      notificationModel.equipmentDesc = notifHeader.equip_desc!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.func_loc_id)) {
      notificationModel.functionalLocation = notifHeader.func_loc_id!;
      notificationModel.functionalLocationDesc = notifHeader.func_loc_desc!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.planner_grp)) {
      notificationModel.plannerGroup = notifHeader.planner_grp!;
      notificationModel.plannerGroupDesc = notifHeader.planner_grp_desc!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.main_work_cntr)) {
      notificationModel.workCenter = notifHeader.main_work_cntr!;
      notificationModel.workCenterDesc = notifHeader.main_work_cntr_desc!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.main_wrk_cntr_plant)) {
      notificationModel.workCenterPlant = notifHeader.main_wrk_cntr_plant!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.maintain_plng_plant)) {
      notificationModel.mainPlanningPlant = notifHeader.maintain_plng_plant!;
      notificationModel.mainPlanningPlantDesc =
          notifHeader.main_work_cntr_desc!;
    }

    /*viewModel.getPerson(notifHeader.getPERSON_NO());
		viewModel.getPersonName(notifHeader.getPERSON_NAME());*/

    if (!Utils.isNullOrEmpty(notifHeader.code_grp)) {
      notificationModel.codeGroup = (notifHeader.code_grp!);
      notificationModel.codeGroupDesc = (notifHeader.code_grp_desc!);
    }

    if (!Utils.isNullOrEmpty(notifHeader.code)) {
      notificationModel.code = (notifHeader.code!);
      notificationModel.codeDesc = (notifHeader.code_desc!);
    }

    if (!Utils.isNullOrEmpty(notifHeader.req_start_dat)) {
      notificationModel.startDate = notifHeader.req_start_dat!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.req_start_tim)) {
      notificationModel.startTime = notifHeader.req_start_tim!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.req_end_dat)) {
      notificationModel.endDate = notifHeader.req_end_dat!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.req_end_tim)) {
      notificationModel.endTime = notifHeader.req_end_tim!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.notif_dat)) {
      notificationModel.notificationDate = notifHeader.notif_dat!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.malfn_start_dat)) {
      notificationModel.malfunctionStartDate = notifHeader.malfn_start_dat!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.malfn_start_time)) {
      notificationModel.malfunctionStartTime = notifHeader.malfn_start_time!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.malfn_end_dat)) {
      notificationModel.malfunctionEndDate = notifHeader.malfn_end_dat!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.malfn_end_tim)) {
      notificationModel.malfunctionEndTime = notifHeader.malfn_end_tim!;
    }

    notificationModel.breakdown =
        Constants.ENABLED == (notifHeader.breakdwn_flag ?? false);

    if (!Utils.isNullOrEmpty(notifHeader.reprtd_by)) {
      notificationModel.reportedBy = notifHeader.reprtd_by!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.user_responsible)) {
      notificationModel.userResponsibel = notifHeader.user_responsible!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.syst_stat)) {
      notificationModel.systemStatus = notifHeader.syst_stat!;
    }

    NOTIF_LONG_TEXT_VIEW? longTextView =
        await getNotificationLongTextView(notifHeader: notifHeader);

    if (longTextView != null) {
      notificationModel.longTextView = longTextView.long_txt!;
    }

    NOTIF_LONG_TEXT_ADD? longTextAdd =
        await getNotificationLongTextAdd(notifHeader: notifHeader);

    if (longTextAdd != null) {
      notificationModel.longTextAdd = longTextAdd.long_txt!;
    }

    if (!Utils.isNullOrEmpty(notifHeader.func_loc_id)) {
      String rigNo = notifHeader.func_loc_id!.substring(0, 3);
      RIG_HEADER? rigHeader = await DbHelper.getRigHeaderByRigNO(rigNo);
      NotificationRigInfoModel rigInfo =
          NotificationRigInfoModel.fromRigHeader(rigHeader);
      notificationModel.rigInfo = rigInfo;
    }

    if(!Utils.isNullOrEmpty(notifHeader.effect)) {
       C_EFFECT_HEADER? effect_header = await DbHelper.getEffectOnStstemByCode(notifHeader.effect!);
       notificationModel.effectCode = effect_header?.effect ?? '';
       notificationModel.effectCodeDesc = effect_header?.effect_desc ?? '';
    }

    if(!Utils.isNullOrEmpty(notifHeader.cond_before_malfn)) {
      C_SYSTEM_AVAIL_COND_HEADER? systemCondition = await DbHelper.getSystemAvailableConditionByCode(notifHeader.cond_before_malfn!);
      notificationModel.systemCondtionCode = systemCondition?.avail_cond ?? '';
      notificationModel.systemConditionDesc = systemCondition?.avail_cond_desc ?? '';
    }

    if(!Utils.isNullOrEmpty(notifHeader.avl_before_malfn.toString())) {
      notificationModel.availabilityBeforeMalfunction = notifHeader.avl_before_malfn.toString();
    }

    return notificationModel;
  }

  static Future<List<NOTIF_USER_STATUS>> getNotificationUserStatus(
      {required String notifNo}) async {
    List<NOTIF_USER_STATUS> existingUserStatusList = [];
    String whereClause =
        NOTIF_USER_STATUS.FIELD_NOTIF_NO + " = '" + notifNo + "'";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_USER_STATUS.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          existingUserStatusList.add(NOTIF_USER_STATUS.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationUserStatus', e.toString());
    }

    return existingUserStatusList;
  }

  static Future<List<C_USER_STATUS_HEADER>> getAllNotificationUserStatus(
      {required String notifType}) async {
    List<C_USER_STATUS_HEADER> userStatuses = [];
    try {
      String whereClause = C_USER_STATUS_HEADER.FIELD_STATUS_PROFILE +
          " IN (SELECT " +
          C_NOTIF_TYPE_HEADER.FIELD_STATUS_PROFILE_NOTIF +
          " FROM " +
          C_NOTIF_TYPE_HEADER.TABLE_NAME +
          " WHERE " +
          C_NOTIF_TYPE_HEADER.FIELD_NOTIF_TYPE +
          " = '" +
          notifType +
          "' )";
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(C_USER_STATUS_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          userStatuses.add(C_USER_STATUS_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getAllNotificationUserStatus', e.toString());
    }
    return userStatuses;
  }

  static Future<NOTIF_LONG_TEXT_VIEW?> getNotificationLongTextView(
      {NOTIF_HEADER? notifHeader,
      NOTIF_ITEM? notifItem,
      NOTIF_CAUSE? notifCause,
      NOTIF_ACTIVITY? notifActivity,
      NOTIF_TASK? notifTask}) async {
    String whereClause;
    if (notifHeader != null) {
      whereClause = NOTIF_LONG_TEXT_VIEW.FIELD_NOTIF_NO +
          " = '" +
          notifHeader.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_VIEW.FIELD_OBJ_TYPE +
          " = '" +
          Constants.NOTIF_LONG_TEXT_CONSTANT_HEADER +
          "'";
    } else if (notifItem != null) {
      whereClause = NOTIF_LONG_TEXT_VIEW.FIELD_NOTIF_NO +
          " = '" +
          notifItem.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_VIEW.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifItem.item_key!, 0]) +
          "'";
    } else if (notifCause != null) {
      whereClause = NOTIF_LONG_TEXT_VIEW.FIELD_NOTIF_NO +
          " = '" +
          notifCause.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_VIEW.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifCause.item_no, notifCause.cause_key]) +
          "'";
    } else if (notifActivity != null) {
      whereClause = NOTIF_LONG_TEXT_VIEW.FIELD_NOTIF_NO +
          " = '" +
          notifActivity.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_VIEW.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d",
              [notifActivity.item_no ?? 0, notifActivity.act_key ?? 0]) +
          "'";
    } else {
      whereClause = NOTIF_LONG_TEXT_VIEW.FIELD_NOTIF_NO +
          " = '" +
          notifTask!.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_VIEW.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifTask.item_no ?? 0, 0]) +
          "'";
    }
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_LONG_TEXT_VIEW.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_LONG_TEXT_VIEW.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationLongTextView', e.toString());
    }
    return null;
  }

  static Future<NOTIF_LONG_TEXT_ADD?> getNotificationLongTextAdd(
      {NOTIF_HEADER? notifHeader,
      NOTIF_ITEM? notifItem,
      NOTIF_CAUSE? notifCause,
      NOTIF_ACTIVITY? notifActivity,
      NOTIF_TASK? notifTask}) async {
    String whereClause;
    if (notifHeader != null) {
      whereClause = NOTIF_LONG_TEXT_ADD.FIELD_NOTIF_NO +
          " = '" +
          notifHeader.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_TYPE +
          " = '" +
          Constants.NOTIF_LONG_TEXT_CONSTANT_HEADER +
          "'";
    } else if (notifItem != null) {
      whereClause = NOTIF_LONG_TEXT_ADD.FIELD_NOTIF_NO +
          " = '" +
          notifItem.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifItem.item_key, 0]) +
          "'";
    } else if (notifCause != null) {
      whereClause = NOTIF_LONG_TEXT_ADD.FIELD_NOTIF_NO +
          " = '" +
          notifCause.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifCause.item_no, notifCause.cause_key]) +
          "'";
    } else if (notifActivity != null) {
      whereClause = NOTIF_LONG_TEXT_ADD.FIELD_NOTIF_NO +
          " = '" +
          notifActivity.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_TYPE +
          " = '" +
          Constants.NOTIF_LONG_TEXT_CONSTANT_ACTIVITY +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifActivity.act_key ?? 0, 0]) +
          "'";
    } else {
      whereClause = NOTIF_LONG_TEXT_ADD.FIELD_NOTIF_NO +
          " = '" +
          notifTask!.notif_no! +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_TYPE +
          " = '" +
          Constants.NOTIF_LONG_TEXT_CONSTANT_TASK +
          "' AND " +
          NOTIF_LONG_TEXT_ADD.FIELD_OBJ_KEY +
          " = '" +
          sprintf("%04d%04d", [notifTask.task_key ?? 0, 0]) +
          "'";
    }
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_LONG_TEXT_ADD.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_LONG_TEXT_ADD.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationLongTextAdd', e.toString());
    }
    return null;
  }

  static Future<List<NOTIF_ITEM>> getNotificationItems(
      {required String notifNo}) async {
    List<NOTIF_ITEM> notifItemsList = [];
    String whereClause = NOTIF_ITEM.FIELD_NOTIF_NO + "= '" + notifNo + "'";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_ITEM.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          notifItemsList.add(NOTIF_ITEM.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItems', e.toString());
    }
    return notifItemsList;
  }

  static Future<List<NotificationListItem>> getNotificationItemsWithCause(
      {required String notifNo}) async {
    List<NotificationListItem> notifItemsList = [];
    String whereClause = NOTIF_ITEM.FIELD_NOTIF_NO + "= '" + notifNo + "'";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_ITEM.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          NOTIF_ITEM notif = NOTIF_ITEM.fromJson(data);
          List<NOTIF_CAUSE> notifCauses = await getNotificationCauses(
              notifNo: notif.notif_no!, notifItemNo: notif.item_key!);
          if (notifCauses.isNotEmpty) {
            notifItemsList.add(NotificationListItem(
                notifItem: notif,
                causeCode: notifCauses[0].cause_code ?? '',
                causeGroup: notifCauses[0].cause_code_grp ?? '',
                causeCodeDesc: notifCauses[0].code_desc ?? ''));
          } else {
            notifItemsList.add(NotificationListItem(
                notifItem: notif,
                causeCode: '',
                causeGroup: '',
                causeCodeDesc: ''));
          }
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItems', e.toString());
    }
    return notifItemsList;
  }

  static Future<NOTIF_ITEM?> getNotificationItem(
      {required final String notifNo, required final int notifItemNo}) async {
    String whereClause = NOTIF_ITEM.FIELD_NOTIF_NO +
        "= '" +
        notifNo +
        "' AND " +
        NOTIF_ITEM.FIELD_ITEM_KEY +
        " = '" +
        notifItemNo.toString() +
        "'";

    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_ITEM.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_ITEM.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItem', e.toString());
    }
  }

  ///IMPLEMENTATION OF getNotificationItemViewModel
  static Future<NotificationItemModel> getNotificationItemModel(
      {required NOTIF_ITEM notifItem,
      required NotificationItemModel viewModel}) async {
    viewModel.notificationNo = notifItem.notif_no ?? '';

    viewModel.itemNo = (notifItem.item_key ?? 0);

    if (!Utils.isNullOrEmpty(notifItem.short_txt)) {
      viewModel.shortText = (notifItem.short_txt!);
    }

    if (!Utils.isNullOrEmpty(notifItem.obj_part_code)) {
      viewModel.objectCode = (notifItem.obj_part_code!);
      viewModel.objectCodeDesc = (notifItem.obj_part_code_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifItem.obj_part_code_grp)) {
      viewModel.objectCodeGroup = (notifItem.obj_part_code_grp!);
      viewModel.objectCodeGroupDesc = (notifItem.obj_part_code_grp_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifItem.dmg_code)) {
      viewModel.damageCode = (notifItem.dmg_code!);
      viewModel.damageCodeDesc = (notifItem.dmg_code_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifItem.dmg_code_grp)) {
      viewModel.damageCodeGroup = (notifItem.dmg_code_grp!);
      viewModel.damageCodeGroupDesc = (notifItem.dmg_code_grp_desc ?? '');
    }

    NOTIF_LONG_TEXT_VIEW? longTextView =
        await getNotificationLongTextView(notifItem: notifItem);

    if (longTextView != null) {
      viewModel.longTextView = (longTextView.long_txt!);
    }

    NOTIF_LONG_TEXT_ADD? longTextAdd =
        await getNotificationLongTextAdd(notifItem: notifItem);

    if (longTextAdd != null) {
      viewModel.longTextAdd = (longTextAdd.long_txt!);
    }

    List<NOTIF_CAUSE> notifCauses = await getNotificationCauses(
        notifNo: notifItem.notif_no!, notifItemNo: notifItem.item_key!);

    if (notifCauses.isNotEmpty) {
      viewModel.causeNo = (notifCauses[0].cause_key!);
      viewModel.causeShortText = (notifCauses[0].short_txt ?? '');

      viewModel.causeCodeGroup = (notifCauses[0].cause_code_grp ?? '');
      viewModel.causeCodeGroupDesc = (notifCauses[0].code_grp_desc ?? '');

      viewModel.causeCode = (notifCauses[0].cause_code ?? '');
      viewModel.causeCodeDesc = (notifCauses[0].code_desc ?? '');

      longTextView =
          await getNotificationLongTextView(notifCause: notifCauses[0]);

      if (longTextView != null) {
        viewModel.causeLongTextView = (longTextView.long_txt!);
      }

      longTextAdd =
          await getNotificationLongTextAdd(notifCause: notifCauses[0]);

      if (longTextAdd != null) {
        viewModel.causeLongTextAdd = (longTextAdd.long_txt!);
      }
    }

    return viewModel;
  }

  static Future<int> getMaxCntOfNotifItem(
      {required NOTIF_HEADER notifHeader}) async {
    String query =
        "SELECT MAX(${NOTIF_ITEM.FIELD_SORT_NO}) AS max_count FROM ${NOTIF_ITEM.TABLE_NAME} WHERE " +
            NOTIF_ITEM.FIELD_NOTIF_NO +
            " ='" +
            notifHeader.notif_no! +
            "'";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return dataList[0]["max_count"] ?? 0;
      }
    } catch (e) {
      Logger.logError(className, 'getMaxCntOfNotifItem', e.toString());
    }
    return 0;
  }

  static Future<List<NOTIF_CAUSE>> getNotificationCauses(
      {required final String notifNo, required final int notifItemNo}) async {
    List<NOTIF_CAUSE> notifCauses = [];
    String whereClause = NOTIF_CAUSE.FIELD_NOTIF_NO +
        "= '" +
        notifNo +
        "' AND " +
        NOTIF_CAUSE.FIELD_ITEM_KEY +
        " = '" +
        notifItemNo.toString() +
        "'";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_CAUSE.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          notifCauses.add(NOTIF_CAUSE.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationCauses', e.toString());
    }
    return notifCauses;
  }

  static Future<NOTIF_CAUSE?> getNotificationCause(
      {required final String notifNo,
      required final double notifItemNo,
      required final String causeKey}) async {
    String whereClause = NOTIF_CAUSE.FIELD_NOTIF_NO +
        "= '" +
        notifNo +
        "' AND " +
        NOTIF_CAUSE.FIELD_ITEM_KEY +
        " = '" +
        notifItemNo.toString() +
        "' AND " +
        NOTIF_CAUSE.FIELD_CAUSE_KEY +
        " = '" +
        causeKey +
        "'";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_CAUSE.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_CAUSE.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationCause', e.toString());
    }
    return null;
  }

  //TODO
  //static NotificationItemCauseViewModel getNotificationItemCauseViewModel(NOTIF_CAUSE notifCause, NotificationItemCauseViewModel viewModel) {}

  static Future<List<ORDER_HEADER>?> getOrdersForHistory(
      {Object? header}) async {
    String whereClause = "";
    if (header != null) {
      if (header is EQUIP_HEADER) {
        EQUIP_HEADER equipHeader = header;
        whereClause = ORDER_HEADER.FIELD_EQUIP_ID +
            "= '" +
            equipHeader.equnr! +
            "' AND " +
            ORDER_HEADER.FIELD_HISTORY_FLAG +
            " = '" +
            Constants.ENABLED +
            "'";
      } else if (header is FUNC_LOC_HEADER) {
        FUNC_LOC_HEADER fLocHeader = header;
        whereClause = ORDER_HEADER.FIELD_FUNC_LOC_ID +
            "= '" +
            fLocHeader.func_loc! +
            "' AND " +
            ORDER_HEADER.FIELD_HISTORY_FLAG +
            " = '" +
            Constants.ENABLED +
            "'";
      }
      List<ORDER_HEADER> ordersList = [];
      try {
        List<dynamic> dataList = await AppDatabaseManager().select(
            DBInputEntity(ORDER_HEADER.TABLE_NAME, {})
              ..setWhereClause(whereClause));
        if (dataList.length > 0) {
          for (var data in dataList) {
            ordersList.add(ORDER_HEADER.fromJson(data));
          }
        }
        return ordersList;
      } catch (e) {
        Logger.logError(className, 'getOrdersForHistory', e.toString());
      }
    }

    return null;
  }

  static void getOrderedNotifList({required List<NOTIF_HEADER> notifs}) {
    if (notifs.length > 0) {
      notifs.sort((a, b) {
        int sComp = a.notif_type.toString().compareTo(b.notif_type.toString());
        if (sComp != 0) {
          return sComp;
        } else {
          return a.notif_type_text
              .toString()
              .compareTo(b.notif_type_text.toString());
        }
      });
    }
  }

  static deleteAllHistory({required String equipNo}) async {
    if (!Utils.isNullOrEmpty(equipNo)) {
      String whereClause = NOTIF_HEADER.FIELD_EQUIP_ID +
          "= '" +
          equipNo +
          "' AND " +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " = '" +
          Constants.ENABLED +
          "'";
      String whereClause1 = ORDER_HEADER.FIELD_EQUIP_ID +
          "= '" +
          equipNo +
          "' AND " +
          ORDER_HEADER.FIELD_HISTORY_FLAG +
          " = '" +
          Constants.ENABLED +
          "'";
      try {
        await AppDatabaseManager().delete(
            DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
              ..setWhereClause(whereClause));

        await AppDatabaseManager().delete(
            DBInputEntity(ORDER_HEADER.TABLE_NAME, {})
              ..setWhereClause(whereClause1));
      } catch (e) {
        Logger.logError(className, 'getOrderedNotifList', e.toString());
      }
    }
  }

  static Future<List<GenericRONModel>> getNotificationsForHistory(
      {Object? header, required BuildContext context}) async {
    List<GenericRONModel> genericModelList = [];
    String whereClause = "";
    if (header != null) {
      if (header is EQUIP_HEADER) {
        EQUIP_HEADER equipHeader = header;
        whereClause = NOTIF_HEADER.FIELD_EQUIP_ID +
            "= '" +
            equipHeader.equnr! +
            "' AND " +
            NOTIF_HEADER.FIELD_HISTORY_FLAG +
            " = '" +
            Constants.ENABLED +
            "' ORDER BY " +
            NOTIF_HEADER.FIELD_REQ_START_DAT +
            " DESC";
      } else if (header is FUNC_LOC_HEADER) {
        FUNC_LOC_HEADER fLocHeader = header;
        whereClause = NOTIF_HEADER.FIELD_FUNC_LOC_ID +
            "= '" +
            fLocHeader.func_loc! +
            "' AND " +
            NOTIF_HEADER.FIELD_HISTORY_FLAG +
            " = '" +
            Constants.ENABLED +
            "' ORDER BY " +
            NOTIF_HEADER.FIELD_REQ_START_DAT +
            " DESC";
      }

      try {
        List<dynamic> dataList = await AppDatabaseManager().select(
            DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
              ..setWhereClause(whereClause));
        if (dataList.length > 0) {
          for (var data in dataList) {
            genericModelList.add(await getNotificationModel(
                notif_header: NOTIF_HEADER.fromJson(data), context: context));
          }
        }
      } catch (e) {
        Logger.logError(className, 'getNotificationsForHistory', e.toString());
      }
    }
    return genericModelList;
  }

  static Future<List<C_CODE_GROUP_HEADER>> getCodeGroup(
      {final String? equipNo,
      final String? funLoc,
      final String? notifType,
      final String? catType}) async {
    List<C_CODE_GROUP_HEADER> codeGroupHeaders = [];

    try {
      if (!Utils.isNullOrEmpty(equipNo)) {
        EQUIP_HEADER? equipHeader =
            await EquipmentHelper.getEquipmentHeaderForDetail(
                equipNo: equipNo!);
        final String catProfile = equipHeader!.org_catlg_profl!;
        if (!Utils.isNullOrEmpty(catProfile)) {
          List<C_CODE_GROUP_HEADER> codeGroups =
              await getCodeGroups(catProfile: catProfile, catType: catType!);

          if (codeGroups.isNotEmpty) {
            return codeGroups;
          }
        }
      }

      if (!Utils.isNullOrEmpty(funLoc)) {
        FUNC_LOC_HEADER? fLocHeader =
            await TechObjectsHelper.getFLocHeader(fLocVal: funLoc!);

        final String catProfile = fLocHeader!.org_catlg_profl!;

        if (!Utils.isNullOrEmpty(catProfile)) {
          List<C_CODE_GROUP_HEADER> codeGroups =
              await getCodeGroups(catProfile: catProfile, catType: catType!);

          if (codeGroups.isNotEmpty) {
            return codeGroups;
          }
        }
      }

      if (!Utils.isNullOrEmpty(notifType)) {
        C_NOTIF_TYPE_HEADER? notifTypeHeader =
            await getNotifTypeHeader(notifType: notifType!);

        if (notifTypeHeader != null) {
          final String catProfile = notifTypeHeader.catalog_profile!;

          if (!Utils.isNullOrEmpty(catProfile)) {
            List<C_CODE_GROUP_HEADER> codeGroups =
                await getCodeGroups(catProfile: catProfile, catType: catType!);

            if (codeGroups.isNotEmpty) {
              return codeGroups;
            }
          }
        }

        List<dynamic> dataList = await AppDatabaseManager().select(
            DBInputEntity(C_CODE_GROUP_HEADER.TABLE_NAME, {})
              ..setWhereClause(C_CODE_GROUP_HEADER.FIELD_CATALOG_TYPE +
                  " = '" +
                  catType! +
                  "'"));
        if (dataList.length > 0) {
          for (var data in dataList) {
            codeGroupHeaders.add(C_CODE_GROUP_HEADER.fromJson(data));
          }
        }
      }
    } catch (e) {
      Logger.logError(className, 'getCodeGroup', e.toString());
    }

    return codeGroupHeaders;
  }

  static Future<List<C_CODE_GROUP_HEADER>> getCodeGroups(
      {required final String catProfile, required final String catType}) async {
    List<C_CODE_GROUP_HEADER> codeGroupHeaders = [];
    final String whereClause = C_CODE_GROUP_HEADER.FIELD_CODE_GROUP +
        " IN (SELECT " +
        C_CAT_PROFILE_HEADER.FIELD_CODE_GROUP +
        " FROM " +
        C_CAT_PROFILE_HEADER.TABLE_NAME +
        " WHERE " +
        C_CAT_PROFILE_HEADER.FIELD_CAT_PROFILE +
        " = '" +
        catProfile +
        "' AND " +
        C_CAT_PROFILE_HEADER.FIELD_CATALOG_TYPE +
        " = '" +
        catType +
        "') AND " +
        C_CAT_PROFILE_HEADER.FIELD_CATALOG_TYPE +
        " = '" +
        catType +
        "' ORDER BY " +
        C_CODE_GROUP_HEADER.FIELD_CODE_GROUP +
        " ASC";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(C_CODE_GROUP_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          codeGroupHeaders.add(C_CODE_GROUP_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getCodeGroups', e.toString());
    }
    return codeGroupHeaders;
  }

  static Future<List<C_NOTIF_TYPE_HEADER>> getNotificationTypes(
      {required final String pMode}) async {
    List<C_NOTIF_TYPE_HEADER> orderTypesList = [];
    String? whereClause;
    if (Constants.P_MODE_ADD == (pMode)) {
      whereClause = C_NOTIF_TYPE_HEADER.FIELD_NOTIF_TYPE +
          " IN (SELECT " +
          C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
          " FROM " +
          C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME +
          " WHERE " +
          C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_ADD +
          " = '" +
          Constants.ENABLED +
          "')";
    } else if (Constants.P_MODE_MODIFY == (pMode)) {
      whereClause = C_NOTIF_TYPE_HEADER.FIELD_NOTIF_TYPE +
          " IN (SELECT " +
          C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
          " FROM " +
          C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME +
          " WHERE " +
          C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_MODIFY +
          " = '" +
          Constants.ENABLED +
          "')";
    }

    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(C_NOTIF_TYPE_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause!));
      if (dataList.length > 0) {
        for (var data in dataList) {
          orderTypesList.add(C_NOTIF_TYPE_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationTypes', e.toString());
    }
    return orderTypesList;
  }

  static Future<C_NOTIF_TYPE_HEADER?> getNotifTypeHeader(
      {required final String notifType}) async {
    String whereClause =
        C_NOTIF_TYPE_HEADER.FIELD_NOTIF_TYPE + " = '" + notifType + "'";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(C_NOTIF_TYPE_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return C_NOTIF_TYPE_HEADER.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotifTypeHeader', e.toString());
    }
    return null;
  }

  static Future<bool> isNotifItemAddAllowed(
      {required final String notifType}) async {
    String whereClause = C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_ITEM_ADD +
        " = '" +
        Constants.ENABLED +
        "' AND " +
        C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
        " = '" +
        notifType +
        "'";

    var query =
        "SELECT COUNT(*) AS count FROM ${C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return 0 < int.parse(dataList[0]["count"]);
      }
    } catch (e) {
      Logger.logError(className, 'isNotifItemAddAllowed', e.toString());
    }
    return false;
  }

  static Future<bool> isNotifItemModifyAllowed(
      {required final String notifType}) async {
    String whereClause = C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_ITEM_MOD +
        " = '" +
        Constants.ENABLED +
        "' AND " +
        C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
        " = '" +
        notifType +
        "'";

    var query =
        "SELECT COUNT(*) AS count FROM ${C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return 0 < int.parse(dataList[0]["count"].toString());
      }
    } catch (e) {
      Logger.logError(className, 'isNotifItemModifyAllowed', e.toString());
    }
    return false;
  }

  static Future<bool> isNotifNocoAllowed(
      {required final String notifType}) async {
    // String whereClause = C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_NOCO +
    //     " = '" +
    //     Constants.ENABLED +
    //     "' AND " +
    //     C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
    //     " = '" +
    //     notifType +
    //     "'";
    String whereClause = "";

    var query =
        "SELECT COUNT(*) AS count FROM ${C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return 0 < int.parse(dataList[0]["count"].toString());
      }
    } catch (e) {
      Logger.logError(className, 'isNotifNocoAllowed', e.toString());
    }
    return false;
  }

  static Future<bool> isFollowUpOrderAllowed(
      {required final String notifType}) async {
    // // String whereClause = C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_ORDER +
    // //     " = '" +
    // //     Constants.ENABLED +
    // //     "' AND " +
    // //     C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
    // //     " = '" +
    // //     notifType +
    // //     "'";

    // String whereClause = "";

    // // var query =
    // //     "SELECT COUNT(*) AS count FROM ${C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME} WHERE $whereClause";

    // var query =
    //     "SELECT COUNT(*) AS count FROM ${C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME}";

    // try {
    //   List<Map<String, dynamic>>? dataList =
    //       await AppDatabaseManager().execute(query);
    //   if (dataList != null && dataList.length > 0) {
    //     return 0 < int.parse(dataList[0]["count"].toString());
    //   }
    // } catch (e) {
    //   Logger.logError(className, 'isFollowUpOrderAllowed', e.toString());
    // }
    // return false;

    return true;
  }

  static Future<bool> isNotifActionPresent(
      {required final String notifNo}) async {
    String whereClause = NOTIF_ACTION.FIELD_NOTIF_NO + " = '" + notifNo + "'";

    var query =
        "SELECT COUNT(*) AS count FROM ${NOTIF_ACTION.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return 0 < int.parse(dataList[0]["count"].toString());
      }
    } catch (e) {
      Logger.logError(className, 'isNotifActionPresent', e.toString());
    }
    return false;
  }

  static Future<bool> isEditable(
      {required final NOTIF_HEADER notifHeader}) async {
    if (!ApplicationHelper.isNotificationEditEnabled) {
      return false;
    }

    if (!Utils.isNullOrEmpty(notifHeader.syst_stat) &&
        notifHeader.syst_stat!.contains(Constants.NOCO)) {
      return false;
    }

    if (Constants.ENABLED == (notifHeader.history_flag)) {
      return false;
    }

    bool sent = false;
    try {
      sent = await SettingsHelper().isInSentItems(notifHeader.lid);
      sent = sent | await SettingsHelper().isInOutBoxQueue(notifHeader.lid);
    } catch (e) {
      Logger.logError(className, 'isEditable', e.toString());
    }

    if (sent) {
      return false;
    }

    // String whereClause = C_NOTIF_TYPE_DETAILS_HEADER.FIELD_ALLOW_MODIFY +
    //     " = '" +
    //     Constants.ENABLED +
    //     "' AND " +
    //     C_NOTIF_TYPE_DETAILS_HEADER.FIELD_NOTIF_TYPE +
    //     " = '" +
    //     notifHeader.notif_type! +
    //     "'";

    String whereClause = "";

    var query =
        "SELECT COUNT(*) AS count FROM ${C_NOTIF_TYPE_DETAILS_HEADER.TABLE_NAME} WHERE $whereClause";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        if (0 == int.parse(dataList[0]["count"].toString())) {
          return false;
        }
      }
    } catch (e) {
      Logger.logError(className, 'isEditable', e.toString());
    }
    return !(await isNotifActionPresent(notifNo: notifHeader.notif_no!));
  }

  static deleteAllHistoryForFLoc({required String fLoc}) async {
    if (!Utils.isNullOrEmpty(fLoc)) {
      final String deleteNotifs = "DELETE FROM " +
          NOTIF_HEADER.TABLE_NAME +
          " WHERE " +
          NOTIF_HEADER.FIELD_FUNC_LOC_ID +
          "= '" +
          fLoc +
          "' AND " +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " = '" +
          Constants.ENABLED +
          "'";
      try {
        await AppDatabaseManager().execute(deleteNotifs);
      } catch (e) {
        Logger.logError(className, 'deleteAllHistoryForFLoc', e.toString());
      }
    }
  }

  static deleteAllHistoryForEquipment({required String equipment}) async {
    if (!Utils.isNullOrEmpty(equipment)) {
      final String deleteNotifs = "DELETE FROM " +
          NOTIF_HEADER.TABLE_NAME +
          " WHERE " +
          NOTIF_HEADER.FIELD_EQUIP_ID +
          "= '" +
          equipment +
          "' AND " +
          NOTIF_HEADER.FIELD_HISTORY_FLAG +
          " = '" +
          Constants.ENABLED +
          "'";
      try {
        await AppDatabaseManager().execute(deleteNotifs);
      } catch (e) {
        Logger.logError(className, 'deleteAllHistoryForFLoc', e.toString());
      }
    }
  }

  static Future<NOTIF_FORM?> getNotifForm({required final String guid}) async {
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_FORM.TABLE_NAME, {})
            ..setWhereClause(NOTIF_FORM.FIELD_FORM_GUID + " ='" + guid + "'"));
      if (dataList.length > 0) {
        return NOTIF_FORM.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError('NotificationHelper', 'getNotifForm', e.toString());
    }
    return null;
  }

  static Future<List<NOTIF_FORM>> getAllNotifForms(
      {required final String notifNo}) async {
    List<NOTIF_FORM> forms = [];
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(DBInputEntity(
          NOTIF_FORM.TABLE_NAME, {})
        ..setWhereClause(NOTIF_FORM.FIELD_NOTIF_NO + " ='" + notifNo + "'"));
      if (dataList.length > 0) {
        for (var data in dataList) {
          forms.add(NOTIF_FORM.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError('NotificationHelper', 'getNotifForm', e.toString());
    }

    return forms;
  }

  static Future<void> markNotifAsUpdate({required final String notifNo}) async {
    final String query = "UPDATE " +
        NOTIF_HEADER.TABLE_NAME +
        " SET " +
        FieldObjectStatus +
        " = '" +
        ObjectStatus.modify.index.toString() +
        "' WHERE " +
        NOTIF_HEADER.FIELD_NOTIF_NO +
        " = '" +
        notifNo +
        "' AND " +
        FieldObjectStatus +
        " <> '" +
        ObjectStatus.add.index.toString() +
        "'";
    try {
      await AppDatabaseManager().execute(query);
    } catch (e) {
      Logger.logError(className, 'markNotifAsUpdate', e.toString());
    }
  }

  static Future<List<NOTIF_HEADER>> getAllModifiedNotifications() async {
    List<NOTIF_HEADER> headers = [];
    final String whereClause = "(" +
        NOTIF_HEADER.FIELD_HISTORY_FLAG +
        " IS NULL OR " +
        NOTIF_HEADER.FIELD_HISTORY_FLAG +
        " = '') AND (" +
        FieldObjectStatus +
        " = '" +
        ObjectStatus.modify.index.toString() +
        "' OR " +
        FieldObjectStatus +
        " = '" +
        ObjectStatus.add.index.toString() +
        "') AND (" +
        FieldSyncStatus +
        " NOT IN (1,2))";
    try {
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          headers.add(NOTIF_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getAllModifiedNotifications', e.toString());
    }
    return headers;
  }

  static Future<List<C_CODE_GROUP_HEADER>> getCodeGroupsByParams(
      {required String? equipNo,
      required String? funLoc,
      required String? notifType,
      String? catType}) async {
    List<C_CODE_GROUP_HEADER> codeGroupHeaders = [];

    try {
      if (!Utils.isNullOrEmpty(equipNo)) {
        EQUIP_HEADER? equipHeader =
            await EquipmentHelper.getEquipmentHeaderForDetail(
                equipNo: equipNo!);
        final String? catProfile = equipHeader!.org_catlg_profl;

        if (!Utils.isNullOrEmpty(catProfile)) {
          List<C_CODE_GROUP_HEADER> codeGroups =
              await getCodeGroups(catProfile: catProfile!, catType: catType!);

          if (codeGroups.isNotEmpty) {
            return codeGroups;
          }
        }
      }

      if (!Utils.isNullOrEmpty(funLoc)) {
        FUNC_LOC_HEADER? fLocHeader =
            await FunctionalLocationHelper.getFLocHeader(floc: funLoc!);
        final String? catProfile = fLocHeader!.org_catlg_profl;

        if (!Utils.isNullOrEmpty(catProfile)) {
          List<C_CODE_GROUP_HEADER> codeGroups =
              await getCodeGroups(catProfile: catProfile!, catType: catType!);

          if (codeGroups.isNotEmpty) {
            return codeGroups;
          }
        }
      }

      if (!Utils.isNullOrEmpty(notifType)) {
        C_NOTIF_TYPE_HEADER? notifTypeHeader =
            await getNotifTypeHeader(notifType: notifType!);

        if (notifTypeHeader != null) {
          final String? catProfile = notifTypeHeader.catalog_profile;

          if (!Utils.isNullOrEmpty(catProfile)) {
            List<C_CODE_GROUP_HEADER> codeGroups =
                await getCodeGroups(catProfile: catProfile!, catType: catType!);

            if (codeGroups.isNotEmpty) {
              return codeGroups;
            }
          }
        }
      }

      List<dynamic> dataList = await AppDatabaseManager().select(DBInputEntity(
          C_CODE_GROUP_HEADER.TABLE_NAME, {})
        ..setWhereClause(
            C_CODE_GROUP_HEADER.FIELD_CATALOG_TYPE + " = '" + catType! + "'"));
      if (dataList.isNotEmpty) {
        for (var data in dataList) {
          codeGroupHeaders.add(C_CODE_GROUP_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(className, 'getCodeGroupsByParams', e.toString());
    }

    return codeGroupHeaders;
  }

  static getNotificationTypesModels() async {
    List<MultiSelectModel> notificationTypesList = [];
    try {
      // IDataStructure[] structures = ApplicationManager.getInstance().getDataManager().get(C_NOTIF_TYPE_HEADER.TABLE_NAME);
      List<dynamic> dataList = await AppDatabaseManager()
          .select(DBInputEntity(C_NOTIF_TYPE_HEADER.TABLE_NAME, {}));
      if (dataList.isNotEmpty) {
        for (var data in dataList) {
          C_NOTIF_TYPE_HEADER header = C_NOTIF_TYPE_HEADER.fromJson(data);

          MultiSelectModel model = MultiSelectModel(
              id: header.notif_type!,
              title: '${header.notif_type} - ${header.short_text!}');
          notificationTypesList.add(model);
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationTypesModels', e.toString());
    }
    return notificationTypesList;
  }

  static getNotificationPriorityModels() async {
    List<MultiSelectModel> multiSelectViewModels = [];
    try {
      /*String query = "SELECT * FROM " + C_PRIORITY_HEADER.TABLE_NAME
					+ " WHERE " + C_PRIORITY_HEADER.FIELD_PRIORITY_TYPE + " IN (SELECT "
					+ C_NOTIF_TYPE_HEADER.FIELD_PRIORITY_TYPE + " FROM " + C_NOTIF_TYPE_HEADER.TABLE_NAME
					+ " WHERE " + C_NOTIF_TYPE_HEADER.FIELD_ORDER_TYPE + " = '" + orderType
					+ "') ORDER BY " + C_PRIORITY_HEADER.FIELD_PRIORITY_DESC;*/

      String query = "SELECT * FROM " +
          C_PRIORITY_HEADER.TABLE_NAME +
          " WHERE " +
          C_PRIORITY_HEADER.FIELD_PRIORITY_TYPE +
          " IN (SELECT " +
          C_NOTIF_TYPE_HEADER.FIELD_PRIORITY_TYPE +
          " FROM " +
          C_NOTIF_TYPE_HEADER.TABLE_NAME +
          ") ORDER BY " +
          C_PRIORITY_HEADER.FIELD_PRIORITY_TYPE;
      //todo NEED TO CHEK SINCE CURSOR IS NOT AVAILABLE IN MOOR
      //Cursor cursorToFormDataStructure = ApplicationManager.getInstance().getDataManager().executeQuery(query);

      // IDataStructure[] structures = DBHelper.getDataStructuresFromCursor(C_PRIORITY_HEADER.TABLE_NAME, cursorToFormDataStructure, C_PRIORITY_HEADER.class);
      List<dynamic> dataList = await AppDatabaseManager().execute(query);
      if (dataList.isNotEmpty) {
        for (var data in dataList) {
          C_PRIORITY_HEADER header = C_PRIORITY_HEADER.fromJson(data);

          MultiSelectModel model = new MultiSelectModel(
            id: header.priority ?? '',
            title:
                '${header.priority_type} - ${header.priority} - ${header.priority_desc}',
          );

          multiSelectViewModels.add(model);
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationPriorityModels', e.toString());
    }

    return multiSelectViewModels;
  }

  static Future<PieChartSyncStatus> getSyncStatusCount() async {
    PieChartSyncStatus pieChartSyncStatus = PieChartSyncStatus();

    try {
      String syncStatusQuery =
          "SELECT $FieldSyncStatus, COUNT(*) AS `count` FROM ${NOTIF_HEADER.TABLE_NAME} GROUP BY $FieldSyncStatus";
      String objectStatusQuery =
          "SELECT $FieldObjectStatus, COUNT(*) AS `count` FROM ${NOTIF_HEADER.TABLE_NAME} GROUP BY $FieldObjectStatus";
      String countQuery =
          "select count(*) as total from ${NOTIF_HEADER.TABLE_NAME}";
      List<dynamic> syncStatusDataList =
          await AppDatabaseManager().execute(syncStatusQuery);
      List<dynamic> objectStatusDataList =
          await AppDatabaseManager().execute(objectStatusQuery);
      List<dynamic> totalCountData =
          await AppDatabaseManager().execute(countQuery);
      if (totalCountData.isNotEmpty) {
        pieChartSyncStatus.totalCount = totalCountData[0]['total'];
      }
      if (syncStatusDataList.isNotEmpty) {
        for (var value in syncStatusDataList) {
          var syncStatus = SyncStatus.values[value["SYNC_STATUS"]];
          if (syncStatus == SyncStatus.none) {
            pieChartSyncStatus.unchangedCount =
                double.parse(value["count"].toString());
          } else if (syncStatus == SyncStatus.sent ||
              syncStatus == SyncStatus.queued) {
            pieChartSyncStatus.queuedOrSendCount +=
                double.parse(value["count"].toString());
            double.parse(value["count"].toString());
          } else if (syncStatus == SyncStatus.error) {
            pieChartSyncStatus.errorCount =
                double.parse(value["count"].toString());
          }
        }
      }
      if (objectStatusDataList.isNotEmpty) {
        for (var value in objectStatusDataList) {
          var objectStatus = ObjectStatus.values[value["OBJECT_STATUS"]];
          if (objectStatus == ObjectStatus.add ||
              objectStatus == ObjectStatus.modify) {
            pieChartSyncStatus.addModifiedCount +=
                double.parse(value["count"].toString());
          }
        }
      }
    } catch (e) {
      Logger.logError(className, 'getSyncStatusPercentage', e.toString());
    }
    return pieChartSyncStatus;
  }

  static Future<int> getSyncStatusTotalCount() async {
    String countQuery =
        "select count(*) as total from ${NOTIF_HEADER.TABLE_NAME}";
    List<dynamic> totalCountData =
        await AppDatabaseManager().execute(countQuery);
    if (totalCountData.isNotEmpty) {
      return totalCountData[0]['total'];
    }
    return 0;
  }

  static _deleteNotificationAction({required NOTIF_HEADER notifHeader}) async {
    List<dynamic> data = await AppDatabaseManager().select(
        DBInputEntity(NOTIF_ACTION.TABLE_NAME, {})
          ..setWhereClause(
              '${NOTIF_ACTION.FIELD_NOTIF_NO} = "${notifHeader.notif_no}"'));
    if (data.isNotEmpty) {
      await AppDatabaseManager().delete(DBInputEntity(
          NOTIF_ACTION.TABLE_NAME, NOTIF_ACTION.fromJson(data[0]).toJson()));
    }
  }

  static Future<List<NOTIF_ACTIVITY>> getNotificationActivityByNotificationNo(
      {required final String notifNo}) async {
    List<NOTIF_ACTIVITY> activityList = [];
    String whereClause = NOTIF_ACTIVITY.FIELD_NOTIF_NO + "= '" + notifNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_ACTIVITY.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.isNotEmpty) {
        for (var data in dataList) {
          NOTIF_ACTIVITY header = NOTIF_ACTIVITY.fromJson(data);
          activityList.add(header);
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItem', e.toString());
    }
    return activityList;
  }

  static Future<List<NOTIF_TASK>> getNotificationTaskListByNotificationNo(
      {required final String notifNo}) async {
    List<NOTIF_TASK> taskList = [];
    String whereClause = NOTIF_TASK.FIELD_NOTIF_NO + "= '" + notifNo + "'";
    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_TASK.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.isNotEmpty) {
        for (var data in dataList) {
          NOTIF_TASK header = NOTIF_TASK.fromJson(data);
          taskList.add(header);
        }
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItem', e.toString());
    }
    return taskList;
  }

  static Future<NOTIF_ACTIVITY?> getNotifActivityByNotifNoAndActivityNo(
      {required final String notifNo,
      required final int notifActivityNo}) async {
    String whereClause = NOTIF_ITEM.FIELD_NOTIF_NO +
        "= '" +
        notifNo +
        "' AND " +
        NOTIF_ACTIVITY.FIELD_ACT_KEY +
        " = '" +
        notifActivityNo.toString() +
        "'";

    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_ACTIVITY.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_ACTIVITY.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItem', e.toString());
    }
    return null;
  }

  static Future<NotifActivityModel> getNotificationActivityModel(
      {required NOTIF_ACTIVITY notifActivity,
      required NotifActivityModel viewModel}) async {
    viewModel.notificationNo = notifActivity.notif_no ?? '';

    viewModel.itemNo = (notifActivity.item_key ?? 0);
    if (!Utils.isNullOrEmpty(notifActivity.act_txt)) {
      viewModel.shortText = (notifActivity.act_txt!);
    }

    if (!Utils.isNullOrEmpty(notifActivity.act_code)) {
      viewModel.actCode = (notifActivity.act_code!);
      viewModel.actCodeDesc = (notifActivity.act_code_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifActivity.act_codegrp)) {
      viewModel.actCodeGroup = (notifActivity.act_codegrp!);
      viewModel.actCodeGroupDesc = (notifActivity.act_codegrp_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifActivity.start_dat)) {
      viewModel.startDate = (notifActivity.start_dat!);
      viewModel.startTime = (notifActivity.start_tim ?? '');
    }

    if (!Utils.isNullOrEmpty(notifActivity.end_dat)) {
      viewModel.endDate = (notifActivity.end_dat!);
      viewModel.endTime = (notifActivity.end_tim ?? '');
    }

    if (!Utils.isNullOrEmpty(notifActivity.act_cat_typ)) {
      viewModel.actCategoryType = (notifActivity.act_cat_typ!);
    }

    NOTIF_LONG_TEXT_VIEW? longTextView =
        await getNotificationLongTextView(notifActivity: notifActivity);

    if (longTextView != null) {
      viewModel.longTextView = (longTextView.long_txt!);
    }

    NOTIF_LONG_TEXT_ADD? longTextAdd =
        await getNotificationLongTextAdd(notifActivity: notifActivity);

    if (longTextAdd != null) {
      viewModel.longTextAdd = (longTextAdd.long_txt!);
    }

    return viewModel;
  }

  static Future<int> getMaxCntOfNotifActivity(
      {required NOTIF_HEADER notifHeader}) async {
    String query =
        "SELECT MAX(${NOTIF_ACTIVITY.FIELD_ACT_SORT_NO}) AS max_count FROM ${NOTIF_ACTIVITY.TABLE_NAME} WHERE " +
            NOTIF_ACTIVITY.FIELD_NOTIF_NO +
            " ='" +
            notifHeader.notif_no! +
            "'";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return dataList[0]["max_count"] ?? 0;
      }
    } catch (e) {
      Logger.logError(className, 'getMaxCntOfNotifActivity', e.toString());
    }
    return 0;
  }

  static Future<NOTIF_TASK?> getNotifTaskByNotifNoAndTaskNo(
      {required final String notifNo,
      required final int notifActivityNo}) async {
    String whereClause = NOTIF_ITEM.FIELD_NOTIF_NO +
        "= '" +
        notifNo +
        "' AND " +
        NOTIF_TASK.FIELD_TASK_KEY +
        " = '" +
        notifActivityNo.toString() +
        "'";

    try {
      List dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_TASK.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        return NOTIF_TASK.fromJson(dataList[0]);
      }
    } catch (e) {
      Logger.logError(className, 'getNotificationItem', e.toString());
    }
    return null;
  }

  static Future<NotifTaskModel> getNotifTaskModel(
      {required NOTIF_TASK notifTask,
      required NotifTaskModel viewModel}) async {
    viewModel.notificationNo = notifTask.notif_no ?? '';

    viewModel.taskKey = (notifTask.task_key ?? 0);

    if (!Utils.isNullOrEmpty(notifTask.short_text)) {
      viewModel.shortText = (notifTask.short_text!);
    }
    if (notifTask.task_key != null) {
      viewModel.taskKey = (notifTask.task_key!);
    }

    if (!Utils.isNullOrEmpty(notifTask.task_code)) {
      viewModel.taskCode = (notifTask.task_code!);
      viewModel.taskCodeDesc = (notifTask.task_code_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifTask.task_codegrp)) {
      viewModel.taskCodeGroup = (notifTask.task_codegrp!);
      viewModel.taskCodeGroupDesc = (notifTask.task_codegrp_desc ?? '');
    }

    if (!Utils.isNullOrEmpty(notifTask.plnd_start_dat)) {
      viewModel.planStartDate = (notifTask.plnd_start_dat!);
      viewModel.planStartTime = (notifTask.plnd_start_tim ?? '');
    }

    if (!Utils.isNullOrEmpty(notifTask.cmpltd_dat)) {
      viewModel.completeDate = (notifTask.cmpltd_dat!);
      viewModel.completeTime = (notifTask.cmpltd_itm ?? '');
    }

    if (!Utils.isNullOrEmpty(notifTask.compltn_persn_nam)) {
      viewModel.personName = (notifTask.compltn_persn_nam!);
      viewModel.personNo = (notifTask.compltn_persn_id!);
    }

    if (!Utils.isNullOrEmpty(notifTask.plnd_end_dat)) {
      viewModel.planEndDate = (notifTask.plnd_end_dat!);
      viewModel.planEndTime = (notifTask.plnd_end_tim ?? '');
    }

    if (!Utils.isNullOrEmpty(notifTask.task_cat_typ)) {
      viewModel.taskCategoryType = (notifTask.task_cat_typ!);
    }
    if (!Utils.isNullOrEmpty(notifTask.prtnr_name)) {
      viewModel.partnerName = (notifTask.prtnr_name!);
    }
    if (!Utils.isNullOrEmpty(notifTask.prtnr_role)) {
      viewModel.partnerRole = (notifTask.prtnr_role!);
    }

    NOTIF_LONG_TEXT_VIEW? longTextView =
        await getNotificationLongTextView(notifTask: notifTask);

    if (longTextView != null) {
      viewModel.longTextView = (longTextView.long_txt!);
    }

    NOTIF_LONG_TEXT_ADD? longTextAdd =
        await getNotificationLongTextAdd(notifTask: notifTask);

    if (longTextAdd != null) {
      viewModel.longTextAdd = (longTextAdd.long_txt!);
    }

    return viewModel;
  }

  static Future<int> getMaxCntOfNotifTask(
      {required NOTIF_HEADER notifHeader}) async {
    String query =
        "SELECT MAX(${NOTIF_TASK.FIELD_TASK_SORT_NO}) AS max_count FROM ${NOTIF_TASK.TABLE_NAME} WHERE " +
            NOTIF_TASK.FIELD_NOTIF_NO +
            " ='" +
            notifHeader.notif_no! +
            "'";
    try {
      List<Map<String, dynamic>>? dataList =
          await AppDatabaseManager().execute(query);
      if (dataList != null && dataList.length > 0) {
        return dataList[0]["max_count"] ?? 0;
      }
    } catch (e) {
      Logger.logError(className, 'getMaxCntOfNotifActivity', e.toString());
    }
    return 0;
  }

  //  Get available Notif User Status for give Notif type
  static Future<List<C_USER_STATUS_HEADER>> getAllNotifUserStatus(
      {required final String notifType}) async {
    List<C_USER_STATUS_HEADER> userStatuses = [];
    try {
      String whereClause = C_USER_STATUS_HEADER.FIELD_STATUS_PROFILE +
          " IN (SELECT " +
          C_NOTIF_TYPE_HEADER.FIELD_STATUS_PROFILE_NOTIF +
          " FROM " +
          C_NOTIF_TYPE_HEADER.TABLE_NAME +
          " WHERE " +
          C_NOTIF_TYPE_HEADER.FIELD_NOTIF_TYPE +
          " = '" +
          notifType +
          "' )";
      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(C_USER_STATUS_HEADER.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          userStatuses.add(C_USER_STATUS_HEADER.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError(
          'NotificationHelper', 'getAllNotifUserStatus', e.toString());
    }
    return userStatuses;
  }

  //  Get User Status for give Notif number
  static Future<List<NOTIF_USER_STATUS>> getNotifUserStatus(
      {required final String notifNo}) async {
    List<NOTIF_USER_STATUS> userStatuses = [];
    try {
      String whereClause =
          NOTIF_USER_STATUS.FIELD_NOTIF_NO + " = '" + notifNo + "'";

      List<dynamic> dataList = await AppDatabaseManager().select(
          DBInputEntity(NOTIF_USER_STATUS.TABLE_NAME, {})
            ..setWhereClause(whereClause));
      if (dataList.length > 0) {
        for (var data in dataList) {
          userStatuses.add(NOTIF_USER_STATUS.fromJson(data));
        }
      }
    } catch (e) {
      Logger.logError('NotificationHelper', 'getNotifUserStatus', e.toString());
    }
    return userStatuses;
  }
}
